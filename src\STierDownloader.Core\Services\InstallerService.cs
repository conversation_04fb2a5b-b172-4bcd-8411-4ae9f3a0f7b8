using System.Diagnostics;
using Microsoft.Extensions.Logging;
using STierDownloader.Core.Models;

namespace STierDownloader.Core.Services;

public interface IInstallerService
{
    Task<bool> InstallSoftwareAsync(Software software, CancellationToken cancellationToken = default);
    Task<bool> UninstallSoftwareAsync(Software software, CancellationToken cancellationToken = default);
    Task<bool> IsSoftwareInstalledAsync(Software software);
    Task<string?> GetInstalledVersionAsync(Software software);
}

public class InstallerService : IInstallerService
{
    private readonly ILogger<InstallerService> _logger;

    public InstallerService(ILogger<InstallerService> logger)
    {
        _logger = logger;
    }

    public async Task<bool> InstallSoftwareAsync(Software software, CancellationToken cancellationToken = default)
    {
        if (!File.Exists(software.InstallerPath))
        {
            _logger.LogError("Installer not found at {Path}", software.InstallerPath);
            return false;
        }

        try
        {
            _logger.LogInformation("Installing {Software} version {Version}", software.Name, software.Version);

            var processInfo = new ProcessStartInfo
            {
                FileName = software.InstallerPath,
                Arguments = software.InstallArguments ?? string.Empty,
                UseShellExecute = false,
                RedirectStandardOutput = true,
                RedirectStandardError = true,
                CreateNoWindow = true
            };

            if (software.RequiresElevation)
            {
                processInfo.Verb = "runas";
                processInfo.UseShellExecute = true;
                processInfo.RedirectStandardOutput = false;
                processInfo.RedirectStandardError = false;
            }

            using var process = Process.Start(processInfo);
            if (process == null)
            {
                _logger.LogError("Failed to start installer process");
                return false;
            }

            await process.WaitForExitAsync(cancellationToken);

            if (process.ExitCode == 0)
            {
                _logger.LogInformation("Successfully installed {Software}", software.Name);
                return true;
            }
            else
            {
                _logger.LogError("Installation failed with exit code {ExitCode}", process.ExitCode);
                return false;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Installation failed for {Software}", software.Name);
            return false;
        }
    }

    public async Task<bool> UninstallSoftwareAsync(Software software, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrEmpty(software.UninstallCommand))
        {
            _logger.LogWarning("No uninstall command configured for {Software}", software.Name);
            return false;
        }

        try
        {
            _logger.LogInformation("Uninstalling {Software}", software.Name);

            var parts = software.UninstallCommand.Split(' ', 2);
            var fileName = parts[0];
            var arguments = parts.Length > 1 ? parts[1] : string.Empty;

            var processInfo = new ProcessStartInfo
            {
                FileName = fileName,
                Arguments = arguments,
                UseShellExecute = false,
                RedirectStandardOutput = true,
                RedirectStandardError = true,
                CreateNoWindow = true
            };

            if (software.RequiresElevation)
            {
                processInfo.Verb = "runas";
                processInfo.UseShellExecute = true;
                processInfo.RedirectStandardOutput = false;
                processInfo.RedirectStandardError = false;
            }

            using var process = Process.Start(processInfo);
            if (process == null)
            {
                _logger.LogError("Failed to start uninstaller process");
                return false;
            }

            await process.WaitForExitAsync(cancellationToken);

            if (process.ExitCode == 0)
            {
                _logger.LogInformation("Successfully uninstalled {Software}", software.Name);
                return true;
            }
            else
            {
                _logger.LogError("Uninstallation failed with exit code {ExitCode}", process.ExitCode);
                return false;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Uninstallation failed for {Software}", software.Name);
            return false;
        }
    }

    public async Task<bool> IsSoftwareInstalledAsync(Software software)
    {
        return await Task.Run(() =>
        {
            // Check registry for installed software
            // This is a simplified check - you may need to customize based on specific software
            var registryPaths = new[]
            {
                @"SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall",
                @"SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Uninstall"
            };

            foreach (var path in registryPaths)
            {
                using var key = Microsoft.Win32.Registry.LocalMachine.OpenSubKey(path);
                if (key == null) continue;

                foreach (var subKeyName in key.GetSubKeyNames())
                {
                    using var subKey = key.OpenSubKey(subKeyName);
                    var displayName = subKey?.GetValue("DisplayName") as string;
                    
                    if (!string.IsNullOrEmpty(displayName) && 
                        displayName.Contains(software.Name, StringComparison.OrdinalIgnoreCase))
                    {
                        return true;
                    }
                }
            }

            return false;
        });
    }

    public async Task<string?> GetInstalledVersionAsync(Software software)
    {
        return await Task.Run(() =>
        {
            var registryPaths = new[]
            {
                @"SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall",
                @"SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Uninstall"
            };

            foreach (var path in registryPaths)
            {
                using var key = Microsoft.Win32.Registry.LocalMachine.OpenSubKey(path);
                if (key == null) continue;

                foreach (var subKeyName in key.GetSubKeyNames())
                {
                    using var subKey = key.OpenSubKey(subKeyName);
                    var displayName = subKey?.GetValue("DisplayName") as string;
                    
                    if (!string.IsNullOrEmpty(displayName) && 
                        displayName.Contains(software.Name, StringComparison.OrdinalIgnoreCase))
                    {
                        return subKey?.GetValue("DisplayVersion") as string;
                    }
                }
            }

            return null;
        });
    }
}