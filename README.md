# S-Tier Downloader

A Windows application for downloading, installing, and managing building automation software including Tridium Niagara 4, VPN clients, and HVAC programming tools.

## Features

- **Centralized Software Management**: Manage all your building automation software from one place
- **Batch Operations**: Download and install multiple software packages at once
- **Progress Tracking**: Real-time download progress with visual indicators
- **Checksum Verification**: Ensure download integrity with SHA-256 verification
- **Admin Elevation**: Automatic elevation for software requiring administrator privileges
- **Version Detection**: Detect installed software versions from Windows registry

## Supported Software

- **Building Automation Systems**
  - Tridium Niagara 4
  - Johnson Controls Metasys
  - Honeywell WEBs-AX
  
- **VPN Clients**
  - FortiClient VPN
  - Other enterprise VPN solutions
  
- **HVAC Programming Tools**
  - Device configuration utilities
  - BACnet tools
  - Custom programming interfaces

## Requirements

- Windows 10/11
- .NET 8.0 Runtime
- Administrator privileges (for most installations)

## Installation

1. Download the latest release from the Releases page
2. Extract the ZIP file to your preferred location
3. Run `STierDownloader.UI.exe` as Administrator

## Configuration

Edit `appsettings.json` to configure:
- Download URLs for your software
- Installation arguments
- Checksum values for verification
- Custom download paths

### Adding Custom Software

Add entries to the `Software` array in `appsettings.json`:

```json
{
  "Name": "Your Software",
  "Version": "1.0",
  "Type": "Utility",
  "DownloadUrl": "https://example.com/download.exe",
  "InstallArguments": "/S",
  "ChecksumSha256": "abc123...",
  "RequiresElevation": true
}
```

## Development

### Prerequisites

- Visual Studio 2022 or later
- .NET 8.0 SDK
- Windows 10/11 SDK

### Building from Source

```bash
# Clone the repository
git clone https://github.com/yourusername/s-tier-downloader.git
cd s-tier-downloader

# Restore dependencies
dotnet restore

# Build the solution
dotnet build

# Run the application
dotnet run --project src/STierDownloader.UI/STierDownloader.UI.csproj
```

### Project Structure

```
s-tier-downloader/
├── src/
│   ├── STierDownloader.Core/        # Core business logic
│   │   ├── Models/                  # Data models
│   │   └── Services/                # Download & Install services
│   ├── STierDownloader.UI/          # WPF User Interface
│   │   ├── ViewModels/              # MVVM ViewModels
│   │   ├── Views/                   # XAML Views
│   │   └── Converters/              # Value converters
│   └── STierDownloader.Installer/   # Installation helpers
└── tests/
    └── STierDownloader.Tests/       # Unit tests
```

## Security Considerations

- Always verify checksums for downloaded files
- Run only from trusted sources
- Review installation arguments before execution
- Keep software URLs private and secure

## License

[Your License Here]

## Support

For issues and feature requests, please use the GitHub Issues page.