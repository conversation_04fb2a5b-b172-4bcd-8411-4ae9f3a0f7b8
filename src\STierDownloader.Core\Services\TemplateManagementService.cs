using System.Text.Json;
using Microsoft.Extensions.Logging;
using STierDownloader.Core.Models;

namespace STierDownloader.Core.Services;

public interface ITemplateManagementService
{
    Task<List<SoftwareTemplate>> GetTemplatesAsync();
    Task<SoftwareTemplate?> GetTemplateByIdAsync(Guid id);
    Task<SoftwareTemplate?> GetTemplateByNameAsync(string name);
    Task<SoftwareTemplate> AddTemplateAsync(SoftwareTemplate template);
    Task<SoftwareTemplate?> UpdateTemplateAsync(SoftwareTemplate template);
    Task<bool> DeleteTemplateAsync(Guid id);
    Task<bool> SaveTemplatesAsync();
    Task<bool> LoadTemplatesAsync();
    Task ResetToDefaultTemplatesAsync();
    SoftwareTemplate? SuggestTemplateForFile(string fileName);
}

public class TemplateManagementService : ITemplateManagementService
{
    private readonly ILogger<TemplateManagementService> _logger;
    private readonly string _configFilePath;
    private SoftwareTemplatesConfiguration _configuration;
    private readonly JsonSerializerOptions _jsonOptions;
    
    public TemplateManagementService(ILogger<TemplateManagementService> logger)
    {
        _logger = logger;
        
        // Store templates configuration in AppData
        var appDataPath = Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData);
        var configDir = Path.Combine(appDataPath, "STierDownloader", "Config");
        Directory.CreateDirectory(configDir);
        
        _configFilePath = Path.Combine(configDir, "software-templates.json");
        
        _jsonOptions = new JsonSerializerOptions
        {
            WriteIndented = true,
            PropertyNameCaseInsensitive = true
        };
        
        _configuration = new SoftwareTemplatesConfiguration();
        
        // Load templates on initialization
        Task.Run(async () => await LoadTemplatesAsync());
    }
    
    public async Task<List<SoftwareTemplate>> GetTemplatesAsync()
    {
        if (_configuration.Templates.Count == 0)
        {
            await LoadTemplatesAsync();
        }
        
        return _configuration.Templates.OrderBy(t => t.TemplateName).ToList();
    }
    
    public async Task<SoftwareTemplate?> GetTemplateByIdAsync(Guid id)
    {
        var templates = await GetTemplatesAsync();
        return templates.FirstOrDefault(t => t.Id == id);
    }
    
    public async Task<SoftwareTemplate?> GetTemplateByNameAsync(string name)
    {
        var templates = await GetTemplatesAsync();
        return templates.FirstOrDefault(t => 
            string.Equals(t.TemplateName, name, StringComparison.OrdinalIgnoreCase));
    }
    
    public async Task<SoftwareTemplate> AddTemplateAsync(SoftwareTemplate template)
    {
        if (string.IsNullOrWhiteSpace(template.TemplateName))
        {
            throw new ArgumentException("Template name is required");
        }
        
        // Check for duplicate names
        var existing = await GetTemplateByNameAsync(template.TemplateName);
        if (existing != null)
        {
            throw new InvalidOperationException($"Template with name '{template.TemplateName}' already exists");
        }
        
        template.Id = Guid.NewGuid();
        template.CreatedDate = DateTime.Now;
        template.ModifiedDate = DateTime.Now;
        template.IsBuiltIn = false; // User-created templates are never built-in
        
        _configuration.Templates.Add(template);
        _configuration.LastModified = DateTime.Now;
        
        await SaveTemplatesAsync();
        
        _logger.LogInformation("Added new template: {Name}", template.TemplateName);
        
        return template;
    }
    
    public async Task<SoftwareTemplate?> UpdateTemplateAsync(SoftwareTemplate template)
    {
        var existing = _configuration.Templates.FirstOrDefault(t => t.Id == template.Id);
        if (existing == null)
        {
            _logger.LogWarning("Template not found for update: {Id}", template.Id);
            return null;
        }
        
        // Check for duplicate names (excluding self)
        var duplicate = _configuration.Templates.FirstOrDefault(t => 
            t.Id != template.Id && 
            string.Equals(t.TemplateName, template.TemplateName, StringComparison.OrdinalIgnoreCase));
        
        if (duplicate != null)
        {
            throw new InvalidOperationException($"Template with name '{template.TemplateName}' already exists");
        }
        
        // Update all properties
        existing.TemplateName = template.TemplateName;
        existing.Description = template.Description;
        existing.SoftwareName = template.SoftwareName;
        existing.Version = template.Version;
        existing.Type = template.Type;
        existing.InstallPath = template.InstallPath;
        existing.MainExecutable = template.MainExecutable;
        existing.InstallArguments = template.InstallArguments;
        existing.UninstallCommand = template.UninstallCommand;
        existing.RequiresElevation = template.RequiresElevation;
        existing.IsZipPackage = template.IsZipPackage;
        existing.CleanupAfterInstall = template.CleanupAfterInstall;
        existing.FileNamePattern = template.FileNamePattern;
        existing.DefaultCloudPath = template.DefaultCloudPath;
        existing.ModifiedDate = DateTime.Now;
        
        _configuration.LastModified = DateTime.Now;
        await SaveTemplatesAsync();
        
        _logger.LogInformation("Updated template: {Name}", existing.TemplateName);
        
        return existing;
    }
    
    public async Task<bool> DeleteTemplateAsync(Guid id)
    {
        var template = _configuration.Templates.FirstOrDefault(t => t.Id == id);
        if (template == null)
        {
            _logger.LogWarning("Template not found for deletion: {Id}", id);
            return false;
        }
        
        _configuration.Templates.Remove(template);
        _configuration.LastModified = DateTime.Now;
        
        await SaveTemplatesAsync();
        
        _logger.LogInformation("Deleted template: {Name}", template.TemplateName);
        
        return true;
    }
    
    public async Task<bool> SaveTemplatesAsync()
    {
        try
        {
            var json = JsonSerializer.Serialize(_configuration, _jsonOptions);
            await File.WriteAllTextAsync(_configFilePath, json);
            
            _logger.LogDebug("Saved {Count} templates to {Path}", _configuration.Templates.Count, _configFilePath);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to save templates configuration");
            return false;
        }
    }
    
    public async Task<bool> LoadTemplatesAsync()
    {
        try
        {
            if (!File.Exists(_configFilePath))
            {
                _logger.LogInformation("Templates configuration not found, creating default");
                _configuration = SoftwareTemplatesConfiguration.CreateDefault();
                await SaveTemplatesAsync();
                return true;
            }
            
            var json = await File.ReadAllTextAsync(_configFilePath);
            var config = JsonSerializer.Deserialize<SoftwareTemplatesConfiguration>(json, _jsonOptions);
            
            if (config != null)
            {
                _configuration = config;
                _logger.LogInformation("Loaded {Count} templates from configuration", _configuration.Templates.Count);
                return true;
            }
            else
            {
                _logger.LogWarning("Invalid templates configuration, creating empty configuration");
                _configuration = SoftwareTemplatesConfiguration.CreateDefault();
                await SaveTemplatesAsync();
                return true;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to load templates configuration, using defaults");
            _configuration = SoftwareTemplatesConfiguration.CreateDefault();
            return false;
        }
    }
    
    public async Task ResetToDefaultTemplatesAsync()
    {
        _logger.LogInformation("Resetting templates to defaults");
        _configuration = SoftwareTemplatesConfiguration.CreateDefault();
        await SaveTemplatesAsync();
    }
    
    public SoftwareTemplate? SuggestTemplateForFile(string fileName)
    {
        if (string.IsNullOrEmpty(fileName) || _configuration.Templates.Count == 0)
            return null;
            
        // Check file patterns if defined
        foreach (var template in _configuration.Templates.Where(t => !string.IsNullOrEmpty(t.FileNamePattern)))
        {
            try
            {
                if (System.Text.RegularExpressions.Regex.IsMatch(fileName, template.FileNamePattern!))
                {
                    _logger.LogDebug("Template '{Template}' matches file pattern for: {File}", 
                        template.TemplateName, fileName);
                    return template;
                }
            }
            catch
            {
                // Invalid regex pattern, skip
            }
        }
        
        // No pattern-based matches found
        return null;
    }
}