namespace STierDownloader.Core.Models;

public class CloudStorageConfig
{
    public string ProjectId { get; set; } = string.Empty;
    public string BucketName { get; set; } = string.Empty;
    public string CredentialsPath { get; set; } = string.Empty;
    public bool UseApplicationDefaultCredentials { get; set; } = false;
    public string? ServiceAccountEmail { get; set; }
    public StorageProvider Provider { get; set; } = StorageProvider.GoogleCloud;
    public string CacheDirectory { get; set; } = "%LOCALAPPDATA%\\STierDownloader\\Cache";
    public bool EnableCaching { get; set; } = true;
}

public enum StorageProvider
{
    Local,
    GoogleCloud,
    AzureBlob,
    AmazonS3
}