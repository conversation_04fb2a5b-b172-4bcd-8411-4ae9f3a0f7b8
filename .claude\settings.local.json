{"permissions": {"allow": ["<PERSON><PERSON>(dir:*)", "Bash(ls:*)", "Bash(find:*)", "Bash(dotnet new:*)", "Bash(where dotnet)", "<PERSON><PERSON>(mkdir:*)", "Bash(git init:*)", "Bash(dotnet build)", "Bash(cmd /c:*)", "<PERSON><PERSON>(powershell:*)", "Bash(\"C:\\Program Files\\dotnet\\dotnet.exe\" build)", "Bash(\"C:\\Program Files\\dotnet\\dotnet.exe\" run --project src/STierDownloader.UI/STierDownloader.UI.csproj)", "Bash(copy:*)", "<PERSON><PERSON>(taskkill:*)"], "deny": []}}