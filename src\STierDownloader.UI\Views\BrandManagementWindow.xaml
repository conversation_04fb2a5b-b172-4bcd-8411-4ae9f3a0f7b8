<Window x:Class="STierDownloader.UI.Views.BrandManagementWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:converters="clr-namespace:STierDownloader.UI.Converters"
        mc:Ignorable="d"
        Title="Manage Niagara Brands"
        Height="600" Width="900"
        WindowStartupLocation="CenterOwner">
    
    <Window.Resources>
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
        <converters:InverseBooleanConverter x:Key="InverseBooleanConverter"/>
        <converters:NullToBooleanConverter x:Key="NullToBooleanConverter"/>
    </Window.Resources>
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="#FF673AB7" Padding="15">
            <StackPanel>
                <TextBlock Text="Niagara Brand Management" 
                          FontSize="20" FontWeight="Bold" Foreground="White"/>
                <TextBlock Text="Add, edit, or remove Niagara brands/variants" 
                          FontSize="12" Foreground="#E1BEE7" Margin="0,3,0,0"/>
            </StackPanel>
        </Border>

        <!-- Content -->
        <Grid Grid.Row="1" Margin="10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="350"/>
            </Grid.ColumnDefinitions>

            <!-- Brand List -->
            <GroupBox Grid.Column="0" Header="Configured Brands" Margin="0,0,5,0">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>
                    
                    <!-- Toolbar -->
                    <ToolBar Grid.Row="0">
                        <Button Content="➕ Add Brand" 
                               Click="AddBrand_Click"
                               Padding="8,4"/>
                        <Separator/>
                        <Button Content="✏️ Edit" 
                               Click="EditBrand_Click"
                               IsEnabled="{Binding ElementName=BrandListView, Path=SelectedItem, Converter={StaticResource NullToBooleanConverter}}"
                               Padding="8,4"/>
                        <Button Content="🗑️ Delete" 
                               Click="DeleteBrand_Click"
                               IsEnabled="{Binding ElementName=BrandListView, Path=SelectedItem, Converter={StaticResource NullToBooleanConverter}}"
                               Padding="8,4"/>
                        <Separator/>
                        <Button Content="🔄 Reset to Defaults" 
                               Click="ResetToDefaults_Click"
                               Padding="8,4"/>
                    </ToolBar>
                    
                    <!-- Brand List -->
                    <ListView Grid.Row="1" 
                             Name="BrandListView"
                             SelectionChanged="BrandListView_SelectionChanged">
                        <ListView.View>
                            <GridView>
                                <GridViewColumn Header="Display Name" 
                                               DisplayMemberBinding="{Binding DisplayName}"
                                               Width="250"/>
                                <GridViewColumn Header="Install Path" 
                                               DisplayMemberBinding="{Binding InstallPath}" 
                                               Width="300"/>
                                <GridViewColumn Header="Main Executable" 
                                               DisplayMemberBinding="{Binding MainExecutable}" 
                                               Width="120"/>
                            </GridView>
                        </ListView.View>
                    </ListView>
                </Grid>
            </GroupBox>

            <!-- Brand Details Panel -->
            <GroupBox Grid.Column="1" Header="Brand Details" Margin="5,0,0,0">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel Name="DetailsPanel" Margin="10">
                        <TextBlock Text="Name:" FontWeight="Bold" Margin="0,5"/>
                        <TextBox Name="NameTextBox"/>
                        
                        <TextBlock Text="Display Name:" FontWeight="Bold" Margin="0,10,0,5"/>
                        <TextBox Name="DisplayNameTextBox"/>
                        
                        <TextBlock Text="Installation Path:" FontWeight="Bold" Margin="0,10,0,5"/>
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <TextBox Grid.Column="0" Name="InstallPathTextBox"/>
                            <Button Grid.Column="1" Content="..." 
                                   Click="BrowseInstallPath_Click"
                                   Margin="5,0,0,0" Padding="8,2"/>
                        </Grid>
                        
                        <TextBlock Text="Main Executable:" FontWeight="Bold" Margin="0,10,0,5"/>
                        <TextBox Name="MainExecutableTextBox" 
                                Text="wb.exe"/>
                        
                        <TextBlock Text="Install Arguments (Optional):" FontWeight="Bold" Margin="0,10,0,5"/>
                        <TextBox Name="InstallArgumentsTextBox"/>
                        
                        <TextBlock Text="Description:" FontWeight="Bold" Margin="0,10,0,5"/>
                        <TextBox Name="DescriptionTextBox" 
                                TextWrapping="Wrap" 
                                AcceptsReturn="True"
                                Height="60"/>
                        
                        <Border BorderBrush="#E0E0E0" BorderThickness="0,1,0,0" Margin="0,15,0,10"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Button Grid.Column="0" Content="Save Changes" 
                                   Click="SaveChanges_Click"
                                   Margin="0,0,5,0" Padding="10,8"
                                   IsEnabled="False"
                                   Name="SaveButton"/>
                            <Button Grid.Column="1" Content="Cancel" 
                                   Click="CancelEdit_Click"
                                   Margin="5,0,0,0" Padding="10,8"
                                   IsEnabled="False"
                                   Name="CancelButton"/>
                        </Grid>
                        
                        <TextBlock Name="StatusText" 
                                  Margin="0,10,0,0"
                                  TextWrapping="Wrap"
                                  Foreground="Green"
                                  FontStyle="Italic"/>
                    </StackPanel>
                </ScrollViewer>
            </GroupBox>
        </Grid>

        <!-- Footer -->
        <Border Grid.Row="2" Background="#F5F5F5" Padding="10">
            <Grid>
                <TextBlock HorizontalAlignment="Left" VerticalAlignment="Center">
                    <Run Text="All brands can be edited. At least one brand must remain." FontStyle="Italic" Foreground="#666"/>
                </TextBlock>
                <Button HorizontalAlignment="Right" 
                       Content="Close" 
                       Click="Close_Click"
                       Padding="20,8"
                       IsDefault="True"/>
            </Grid>
        </Border>
    </Grid>
</Window>