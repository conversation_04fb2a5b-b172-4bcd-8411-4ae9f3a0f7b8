{"Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "CloudStorage": {"Provider": "GoogleCloud", "ProjectId": "your-project-id", "BucketName": "your-bucket-name", "CredentialsPath": "gcs-credentials.json", "UseApplicationDefaultCredentials": false, "ServiceAccountEmail": "<EMAIL>", "CacheDirectory": "%LOCALAPPDATA%\\STierDownloader\\Cache", "EnableCaching": true}, "DownloadSettings": {"DownloadPath": "%LOCALAPPDATA%\\STierDownloader\\Downloads", "MaxConcurrentDownloads": 3, "RetryCount": 3, "TimeoutSeconds": 300}, "Software": [{"Name": "7-<PERSON><PERSON>", "Version": "23.01", "Type": "Utility", "DownloadUrl": "https://www.7-zip.org/a/7z2301-x64.exe", "InstallArguments": "/S", "ChecksumSha256": "d5c4db4e59b2e34c5b93e967e37deb8a2e67b1c6e3b8e3e8e2e1", "RequiresElevation": true}, {"Name": "Notepad++", "Version": "8.6.0", "Type": "Utility", "DownloadUrl": "https://github.com/notepad-plus-plus/notepad-plus-plus/releases/download/v8.6/npp.8.6.Installer.x64.exe", "InstallArguments": "/S", "RequiresElevation": true}, {"Name": "Python", "Version": "3.12.0", "Type": "Framework", "DownloadUrl": "https://www.python.org/ftp/python/3.12.0/python-3.12.0-amd64.exe", "InstallArguments": "/quiet InstallAllUsers=1 PrependPath=1", "UninstallCommand": "msiexec /x {PRODUCT_CODE} /quiet", "RequiresElevation": true}, {"Name": "Git for Windows", "Version": "2.43.0", "Type": "Utility", "DownloadUrl": "https://github.com/git-for-windows/git/releases/download/v2.43.0.windows.1/Git-2.43.0-64-bit.exe", "InstallArguments": "/VERYSILENT /NORESTART", "RequiresElevation": true}, {"Name": "Visual Studio Code", "Version": "1.85.0", "Type": "Utility", "DownloadUrl": "https://update.code.visualstudio.com/1.85.0/win32-x64/stable", "InstallArguments": "/VERYSILENT /NORESTART /MERGETASKS=!runcode", "RequiresElevation": true}, {"Name": "Node.js", "Version": "20.10.0", "Type": "Framework", "DownloadUrl": "https://nodejs.org/dist/v20.10.0/node-v20.10.0-x64.msi", "InstallArguments": "/quiet", "UninstallCommand": "msiexec /x {PRODUCT_CODE} /quiet", "RequiresElevation": true}, {"Name": "Tridium Niagara 4", "Version": "4.13", "Type": "TridiumNiagara", "StorageLocation": "GoogleCloudStorage", "CloudObjectName": "software/niagara/niagara-4.13-installer.exe", "InstallArguments": "/S /v/qn", "ChecksumSha256": "", "RequiresElevation": true}, {"Name": "FortiClient VPN", "Version": "7.2.0", "Type": "VpnClient", "StorageLocation": "GoogleCloudStorage", "CloudObjectName": "software/vpn/FortiClient_7.2.0.msi", "UseSignedUrl": false, "InstallArguments": "/quiet /norestart", "UninstallCommand": "msiexec /x {12345678-1234-1234-1234-123456789012} /quiet", "RequiresElevation": true}, {"Name": "BACnet Discovery Tool", "Version": "2.0.1", "Type": "Utility", "StorageLocation": "GoogleCloudStorage", "CloudObjectName": "software/tools/bacnet-discovery-2.0.1.zip", "UseSignedUrl": true, "SignedUrlExpirationHours": 48, "RequiresElevation": false}, {"Name": "Local Test File", "Version": "1.0", "Type": "Utility", "StorageLocation": "LocalFile", "DownloadUrl": "C:\\Installers\\test-installer.exe", "InstallArguments": "/S", "RequiresElevation": false}]}