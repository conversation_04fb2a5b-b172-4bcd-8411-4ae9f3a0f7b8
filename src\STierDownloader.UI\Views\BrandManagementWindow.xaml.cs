using System.Windows;
using System.Windows.Controls;
using Microsoft.Extensions.Logging;
using Microsoft.Win32;
using STierDownloader.Core.Models;
using STierDownloader.Core.Services;

namespace STierDownloader.UI.Views;

public partial class BrandManagementWindow : Window
{
    private readonly IBrandManagementService? _brandService;
    private readonly ILogger _logger;
    private List<NiagaraBrand> _brands = new();
    private NiagaraBrand? _selectedBrand;
    private bool _isEditing = false;
    private NiagaraBrand? _editingBrand;
    
    public BrandManagementWindow(IBrandManagementService? brandService, ILogger logger)
    {
        InitializeComponent();
        _brandService = brandService;
        _logger = logger;
        
        LoadBrands();
    }
    
    private async void LoadBrands()
    {
        if (_brandService == null)
        {
            StatusText.Text = "Brand service not available";
            StatusText.Foreground = System.Windows.Media.Brushes.Red;
            return;
        }
        
        try
        {
            _brands = await _brandService.GetBrandsAsync();
            BrandListView.ItemsSource = _brands;
            
            if (_brands.Count > 0)
            {
                BrandListView.SelectedIndex = 0;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to load brands");
            StatusText.Text = "Failed to load brands";
            StatusText.Foreground = System.Windows.Media.Brushes.Red;
        }
    }
    
    private void BrandListView_SelectionChanged(object sender, SelectionChangedEventArgs e)
    {
        _selectedBrand = BrandListView.SelectedItem as NiagaraBrand;
        
        if (_selectedBrand != null && !_isEditing)
        {
            DisplayBrandDetails(_selectedBrand);
        }
    }
    
    private void DisplayBrandDetails(NiagaraBrand brand)
    {
        NameTextBox.Text = brand.Name;
        DisplayNameTextBox.Text = brand.DisplayName;
        InstallPathTextBox.Text = brand.InstallPath;
        MainExecutableTextBox.Text = brand.MainExecutable;
        InstallArgumentsTextBox.Text = brand.InstallArguments ?? "";
        DescriptionTextBox.Text = brand.Description ?? "";
        
        // All brands are now editable
        NameTextBox.IsReadOnly = false;
        DisplayNameTextBox.IsReadOnly = false;
        InstallPathTextBox.IsReadOnly = false;
        MainExecutableTextBox.IsReadOnly = false;
        InstallArgumentsTextBox.IsReadOnly = false;
    }
    
    private void AddBrand_Click(object sender, RoutedEventArgs e)
    {
        _isEditing = true;
        _editingBrand = new NiagaraBrand
        {
            Name = "NewBrand",
            DisplayName = "New Brand",
            InstallPath = @"C:\Program Files\NewBrand",
            MainExecutable = "wb.exe"
        };
        
        // Clear and enable all fields
        NameTextBox.Text = _editingBrand.Name;
        DisplayNameTextBox.Text = _editingBrand.DisplayName;
        InstallPathTextBox.Text = _editingBrand.InstallPath;
        MainExecutableTextBox.Text = _editingBrand.MainExecutable;
        InstallArgumentsTextBox.Text = "";
        DescriptionTextBox.Text = "";
        
        NameTextBox.IsReadOnly = false;
        DisplayNameTextBox.IsReadOnly = false;
        InstallPathTextBox.IsReadOnly = false;
        MainExecutableTextBox.IsReadOnly = false;
        InstallArgumentsTextBox.IsReadOnly = false;
        
        SaveButton.IsEnabled = true;
        CancelButton.IsEnabled = true;
        
        StatusText.Text = "Enter details for the new brand";
        StatusText.Foreground = System.Windows.Media.Brushes.Blue;
        
        NameTextBox.Focus();
        NameTextBox.SelectAll();
    }
    
    private void EditBrand_Click(object sender, RoutedEventArgs e)
    {
        if (_selectedBrand == null) return;
        
        _isEditing = true;
        _editingBrand = _selectedBrand;
        
        SaveButton.IsEnabled = true;
        CancelButton.IsEnabled = true;
        
        StatusText.Text = "Editing brand - make changes and click Save";
        StatusText.Foreground = System.Windows.Media.Brushes.Blue;
    }
    
    private async void DeleteBrand_Click(object sender, RoutedEventArgs e)
    {
        if (_selectedBrand == null || _brandService == null) return;
        
        var result = System.Windows.MessageBox.Show(
            $"Are you sure you want to delete the brand '{_selectedBrand.DisplayName}'?",
            "Confirm Delete",
            MessageBoxButton.YesNo,
            MessageBoxImage.Question);
        
        if (result == MessageBoxResult.Yes)
        {
            try
            {
                await _brandService.DeleteBrandAsync(_selectedBrand.Id);
                LoadBrands();
                
                StatusText.Text = $"Deleted brand: {_selectedBrand.DisplayName}";
                StatusText.Foreground = System.Windows.Media.Brushes.Green;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to delete brand");
                StatusText.Text = $"Failed to delete: {ex.Message}";
                StatusText.Foreground = System.Windows.Media.Brushes.Red;
            }
        }
    }
    
    private async void SaveChanges_Click(object sender, RoutedEventArgs e)
    {
        if (_brandService == null) return;
        
        // Validate inputs
        if (string.IsNullOrWhiteSpace(NameTextBox.Text))
        {
            StatusText.Text = "Name is required";
            StatusText.Foreground = System.Windows.Media.Brushes.Red;
            return;
        }
        
        if (string.IsNullOrWhiteSpace(DisplayNameTextBox.Text))
        {
            StatusText.Text = "Display name is required";
            StatusText.Foreground = System.Windows.Media.Brushes.Red;
            return;
        }
        
        if (string.IsNullOrWhiteSpace(InstallPathTextBox.Text))
        {
            StatusText.Text = "Install path is required";
            StatusText.Foreground = System.Windows.Media.Brushes.Red;
            return;
        }
        
        try
        {
            if (_editingBrand != null)
            {
                _editingBrand.Name = NameTextBox.Text.Trim();
                _editingBrand.DisplayName = DisplayNameTextBox.Text.Trim();
                _editingBrand.InstallPath = InstallPathTextBox.Text.Trim();
                _editingBrand.MainExecutable = string.IsNullOrWhiteSpace(MainExecutableTextBox.Text) 
                    ? "wb.exe" : MainExecutableTextBox.Text.Trim();
                _editingBrand.InstallArguments = string.IsNullOrWhiteSpace(InstallArgumentsTextBox.Text) 
                    ? null : InstallArgumentsTextBox.Text.Trim();
                _editingBrand.Description = string.IsNullOrWhiteSpace(DescriptionTextBox.Text) 
                    ? null : DescriptionTextBox.Text.Trim();
                
                if (_editingBrand.Id == Guid.Empty)
                {
                    // New brand
                    await _brandService.AddBrandAsync(_editingBrand);
                    StatusText.Text = $"Added brand: {_editingBrand.DisplayName}";
                }
                else
                {
                    // Update existing
                    await _brandService.UpdateBrandAsync(_editingBrand);
                    StatusText.Text = $"Updated brand: {_editingBrand.DisplayName}";
                }
                
                StatusText.Foreground = System.Windows.Media.Brushes.Green;
                
                _isEditing = false;
                _editingBrand = null;
                SaveButton.IsEnabled = false;
                CancelButton.IsEnabled = false;
                
                LoadBrands();
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to save brand");
            StatusText.Text = $"Failed to save: {ex.Message}";
            StatusText.Foreground = System.Windows.Media.Brushes.Red;
        }
    }
    
    private void CancelEdit_Click(object sender, RoutedEventArgs e)
    {
        _isEditing = false;
        _editingBrand = null;
        SaveButton.IsEnabled = false;
        CancelButton.IsEnabled = false;
        
        if (_selectedBrand != null)
        {
            DisplayBrandDetails(_selectedBrand);
        }
        
        StatusText.Text = "";
    }
    
    private void BrowseInstallPath_Click(object sender, RoutedEventArgs e)
    {
        // Simple text input dialog for folder path
        var inputDialog = new System.Windows.Window
        {
            Title = "Enter Installation Path",
            Width = 500,
            Height = 200,
            WindowStartupLocation = WindowStartupLocation.CenterOwner,
            Owner = this
        };
        
        var panel = new StackPanel { Margin = new Thickness(10) };
        var label = new TextBlock 
        { 
            Text = "Enter the installation directory path:",
            Margin = new Thickness(0, 0, 0, 10)
        };
        var textBox = new System.Windows.Controls.TextBox 
        { 
            Text = InstallPathTextBox.Text,
            Margin = new Thickness(0, 0, 0, 10)
        };
        var buttonPanel = new StackPanel 
        { 
            Orientation = System.Windows.Controls.Orientation.Horizontal,
            HorizontalAlignment = System.Windows.HorizontalAlignment.Right
        };
        var okButton = new System.Windows.Controls.Button 
        { 
            Content = "OK",
            Width = 75,
            Margin = new Thickness(0, 0, 5, 0),
            IsDefault = true
        };
        var cancelButton = new System.Windows.Controls.Button 
        { 
            Content = "Cancel",
            Width = 75,
            IsCancel = true
        };
        
        okButton.Click += (s, args) => { inputDialog.DialogResult = true; };
        cancelButton.Click += (s, args) => { inputDialog.DialogResult = false; };
        
        buttonPanel.Children.Add(okButton);
        buttonPanel.Children.Add(cancelButton);
        panel.Children.Add(label);
        panel.Children.Add(textBox);
        panel.Children.Add(buttonPanel);
        inputDialog.Content = panel;
        
        if (inputDialog.ShowDialog() == true)
        {
            InstallPathTextBox.Text = textBox.Text;
        }
    }
    
    private async void ResetToDefaults_Click(object sender, RoutedEventArgs e)
    {
        if (_brandService == null) return;
        
        var result = System.Windows.MessageBox.Show(
            "This will reset all brands to the default configuration. Custom brands will be lost. Continue?",
            "Reset to Defaults",
            MessageBoxButton.YesNo,
            MessageBoxImage.Warning);
        
        if (result == MessageBoxResult.Yes)
        {
            try
            {
                await _brandService.ResetToDefaultBrandsAsync();
                LoadBrands();
                
                StatusText.Text = "Reset to default brands";
                StatusText.Foreground = System.Windows.Media.Brushes.Green;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to reset brands");
                StatusText.Text = $"Failed to reset: {ex.Message}";
                StatusText.Foreground = System.Windows.Media.Brushes.Red;
            }
        }
    }
    
    private void Close_Click(object sender, RoutedEventArgs e)
    {
        DialogResult = true;
        Close();
    }
}