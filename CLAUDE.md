# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

**S-Tier Downloader** - A Windows application for downloading, installing, and managing building automation software including Tridium Niagara 4, VPN clients, and HVAC programming tools.

## Technology Stack

- **Framework**: .NET 9.0 with C# 13
- **UI Framework**: WPF (Windows Presentation Foundation)
- **Architecture**: MVVM (Model-View-ViewModel) pattern
- **Key Libraries**:
  - CommunityToolkit.Mvvm - MVVM helpers and source generators
  - Microsoft.Extensions.DependencyInjection - Dependency injection
  - Microsoft.Extensions.Hosting - Application hosting
  - Microsoft.Extensions.Logging - Structured logging

## Development Commands

```bash
# Build the solution
dotnet build

# Run the UI application
dotnet run --project src/STierDownloader.UI/STierDownloader.UI.csproj

# Run tests
dotnet test

# Publish for Windows (self-contained)
dotnet publish src/STierDownloader.UI/STierDownloader.UI.csproj -c Release -r win-x64 --self-contained

# Clean build artifacts
dotnet clean
```

## Project Architecture

### Solution Structure
```
STierDownloader.sln
├── STierDownloader.Core         # Core business logic and services
├── STierDownloader.UI           # WPF user interface  
├── STierDownloader.Installer    # Installation utilities
└── STierDownloader.Tests        # Unit and integration tests
```

### Key Components

1. **Core Services** (`STierDownloader.Core/Services/`)
   - `DownloadService`: HTTP downloads with progress tracking and checksum verification
   - `InstallerService`: Software installation/uninstallation with registry detection
   - `UpdateService`: (Future) Check for software updates

2. **Models** (`STierDownloader.Core/Models/`)
   - `Software`: Represents downloadable/installable software with metadata
   - `SoftwareType`: Enum for categorizing software (Niagara, VPN, HVAC tools)

3. **ViewModels** (`STierDownloader.UI/ViewModels/`)
   - `MainViewModel`: Main window logic and software list management
   - `SoftwareViewModel`: Individual software item with download/install commands

4. **Configuration**
   - `appsettings.json`: Software definitions, download URLs, install arguments
   - Downloads stored in: `%LOCALAPPDATA%\STierDownloader\Downloads`

## Working with the Codebase

### Adding New Software Types

1. Add enum value to `SoftwareType` in `Models/Software.cs`
2. Add configuration entry to `appsettings.json`
3. Implement any special installation logic in `InstallerService`

### Implementing Download Features

- Downloads use `HttpClient` with progress reporting via `IProgress<double>`
- Checksum verification with SHA-256
- Automatic retry with configurable attempts
- Concurrent download support

### UI Modifications

- XAML views in `Views/` folder use WPF with data binding
- ViewModels use CommunityToolkit.Mvvm for INPC and commands
- Styles defined in `Styles/Colors.xaml` and `Styles/Controls.xaml`

### Security Considerations

- Application manifest requires administrator elevation
- All installers run with elevated privileges when `RequiresElevation = true`
- Checksum verification before installation
- Download URLs should be HTTPS

## Testing Approach

- Unit tests for services using xUnit
- Mock `HttpClient` for download testing
- Test registry operations carefully (may require admin)
- Integration tests for end-to-end scenarios

## Important Notes

- **Windows Only**: Uses Windows-specific features (Registry, WPF, .NET Windows targeting)
- **Admin Rights**: Most operations require administrator privileges
- **Large Files**: Consider implementing resume capability for large downloads
- **Error Handling**: Comprehensive logging throughout, check logs in `%LOCALAPPDATA%\STierDownloader\Logs`