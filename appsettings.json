{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Information"
    }
  },
  "CloudStorage": {
    "Provider": "GoogleCloud",
    "ProjectId": "s-tier-downloader",
    "BucketName": "s-tier-software",
    "CredentialsPath": "gcs-credentials.json",
    "UseApplicationDefaultCredentials": false,
    "ServiceAccountEmail": "<EMAIL>",
    "CacheDirectory": "%LOCALAPPDATA%\\STierDownloader\\Cache",
    "EnableCaching": true,
    "EnableConfigSync": true,
    "AutoSyncOnStartup": true,
    "SyncIntervalMinutes": 30
  },
  "DownloadSettings": {
    "DownloadPath": "%LOCALAPPDATA%\\STierDownloader\\Downloads",
    "MaxConcurrentDownloads": 3,
    "RetryCount": 3,
    "TimeoutSeconds": 300
  },
  "Software": [
    // Add your software entries here. Examples:
    //
    // HTTP Download Example:
    // {
    //   "Name": "Example Software",
    //   "Version": "1.0",
    //   "Type": "Utility",
    //   "DownloadUrl": "https://example.com/installer.exe",
    //   "InstallArguments": "/S",
    //   "RequiresElevation": true
    // },
    //
    // Google Cloud Storage Example:
    // {
    //   "Name": "Niagara 4 Service Pack",
    //   "Version": "4.13.1",
    //   "Type": "TridiumNiagara",
    //   "StorageLocation": "GoogleCloudStorage",
    //   "CloudObjectName": "software/niagara/niagara-4.13.1-sp.exe",
    //   "InstallArguments": "/S /v/qn",
    //   "ChecksumSha256": "",
    //   "RequiresElevation": true
    // }
  ]
}