# PowerShell script to help identify and fix corrupt metadata in Google Cloud Storage
# This will help find files with problematic names like "(vl < CompanyName)"

Write-Host "Google Cloud Storage Metadata Checker" -ForegroundColor Cyan
Write-Host "======================================" -ForegroundColor Cyan
Write-Host ""

# You'll need to have gsutil installed and configured
# Check if gsutil is available
$gsutilPath = Get-Command gsutil -ErrorAction SilentlyContinue

if (-not $gsutilPath) {
    Write-Host "gsutil is not installed or not in PATH" -ForegroundColor Red
    Write-Host "Please install Google Cloud SDK first: https://cloud.google.com/sdk/docs/install" -ForegroundColor Yellow
    exit 1
}

$bucketName = "s-tier-software"
Write-Host "Checking bucket: gs://$bucketName" -ForegroundColor Green
Write-Host ""

# List all objects with their metadata
Write-Host "Fetching object metadata..." -ForegroundColor Yellow
$objects = gsutil ls -L "gs://$bucketName/**" 2>$null | Out-String

# Parse the output to find objects with suspicious metadata
$lines = $objects -split "`n"
$currentObject = ""
$problematicFiles = @()

foreach ($line in $lines) {
    if ($line -match "^gs://") {
        $currentObject = $line.Trim().TrimEnd(":")
    }
    elseif ($line -match "software-name:\s*(.+)") {
        $softwareName = $matches[1]
        if ($softwareName -match "\(vl\s*<" -or $softwareName -match "<\s*CompanyName") {
            $problematicFiles += [PSCustomObject]@{
                Object = $currentObject
                BadName = $softwareName
            }
            Write-Host "Found problematic file:" -ForegroundColor Red
            Write-Host "  Object: $currentObject" -ForegroundColor White
            Write-Host "  Bad Name: $softwareName" -ForegroundColor Yellow
            Write-Host ""
        }
    }
}

if ($problematicFiles.Count -eq 0) {
    Write-Host "No files found with problematic metadata" -ForegroundColor Green
}
else {
    Write-Host ""
    Write-Host "Summary: Found $($problematicFiles.Count) file(s) with problematic metadata" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "To fix these files, you can:" -ForegroundColor Cyan
    Write-Host "1. Re-upload them with correct metadata through the Upload window" -ForegroundColor White
    Write-Host "2. Use gsutil to update metadata:" -ForegroundColor White
    Write-Host '   gsutil setmeta -h "x-goog-meta-software-name:Vykon Niagara 4" gs://bucket/file' -ForegroundColor Gray
    Write-Host ""
    
    # Offer to generate fix commands
    $response = Read-Host "Would you like to generate fix commands? (y/n)"
    if ($response -eq 'y') {
        Write-Host ""
        Write-Host "Fix Commands:" -ForegroundColor Green
        foreach ($file in $problematicFiles) {
            # Try to guess the correct name
            $suggestedName = "Vykon Niagara 4"  # Default suggestion
            if ($file.Object -match "vykon" -or $file.BadName -match "vykon") {
                $suggestedName = "Vykon Niagara 4"
            }
            elseif ($file.Object -match "honeywell") {
                $suggestedName = "Honeywell WEBs-N4"
            }
            elseif ($file.Object -match "distech") {
                $suggestedName = "Distech EC-Net"
            }
            
            Write-Host "gsutil setmeta -h `"x-goog-meta-software-name:$suggestedName`" `"$($file.Object)`"" -ForegroundColor White
        }
    }
}

Write-Host ""
Write-Host "Done!" -ForegroundColor Green