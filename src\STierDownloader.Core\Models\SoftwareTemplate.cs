namespace STierDownloader.Core.Models;

/// <summary>
/// Template for pre-filling software upload forms
/// </summary>
public class SoftwareTemplate
{
    public Guid Id { get; set; } = Guid.NewGuid();
    public string TemplateName { get; set; } = string.Empty;
    public string? Description { get; set; }
    public DateTime CreatedDate { get; set; } = DateTime.Now;
    public DateTime ModifiedDate { get; set; } = DateTime.Now;
    public bool IsBuiltIn { get; set; }
    
    // Software properties that can be templated
    public string? SoftwareName { get; set; }
    public string? Version { get; set; }
    public SoftwareType? Type { get; set; }
    public string? InstallPath { get; set; }
    public string? MainExecutable { get; set; }
    public string? InstallArguments { get; set; }
    public string? UninstallCommand { get; set; }
    public bool? RequiresElevation { get; set; }
    public bool? IsZipPackage { get; set; }
    public bool? CleanupAfterInstall { get; set; }
    
    // Additional template properties
    public string? FileNamePattern { get; set; } // Regex pattern to match files
    public string? DefaultCloudPath { get; set; } // Default cloud storage path
}

public class SoftwareTemplatesConfiguration
{
    public List<SoftwareTemplate> Templates { get; set; } = new();
    public DateTime LastModified { get; set; } = DateTime.Now;
    public int Version { get; set; } = 1;
    
    public static SoftwareTemplatesConfiguration CreateDefault()
    {
        return new SoftwareTemplatesConfiguration
        {
            Templates = new List<SoftwareTemplate>() // Start with empty list
        };
    }
}