using Microsoft.Extensions.Logging;
using Microsoft.Win32;
using STierDownloader.Core.Models;
using System.Text.RegularExpressions;

namespace STierDownloader.Core.Services;

public interface IInstallationDetectionService
{
    Task<InstallationInfo?> DetectInstallationAsync(Software software);
    Task<string?> FindInstallationDirectoryAsync(string softwareName);
    Task<bool> VerifyInstallationAsync(string installPath, string? mainExecutable = null);
}

public class InstallationDetectionService : IInstallationDetectionService
{
    private readonly ILogger<InstallationDetectionService> _logger;
    private readonly IEnhancedInstallerService _installerService;
    
    // Common installation root directories to check
    private readonly string[] _commonInstallRoots = new[]
    {
        @"C:\Program Files",
        @"C:\Program Files (x86)",
        Environment.GetFolderPath(Environment.SpecialFolder.ProgramFiles),
        Environment.GetFolderPath(Environment.SpecialFolder.ProgramFilesX86),
        @"C:\ProgramData",
        Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData), "Programs"),
        Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "Programs")
    };

    public InstallationDetectionService(
        ILogger<InstallationDetectionService> logger,
        IEnhancedInstallerService installerService)
    {
        _logger = logger;
        _installerService = installerService;
    }

    public async Task<InstallationInfo?> DetectInstallationAsync(Software software)
    {
        _logger.LogInformation("Detecting installation for {Software}", software.Name);
        
        // Priority 1: Check expected install path if specified
        if (!string.IsNullOrEmpty(software.ExpectedInstallPath))
        {
            if (await VerifyInstallationAsync(software.ExpectedInstallPath, software.MainExecutable))
            {
                _logger.LogInformation("Found installation at expected path: {Path}", software.ExpectedInstallPath);
                return new InstallationInfo
                {
                    IsInstalled = true,
                    InstallPath = software.ExpectedInstallPath,
                    DetectionMethod = "Expected Path",
                    Version = await GetVersionFromDirectory(software.ExpectedInstallPath, software.MainExecutable)
                };
            }
        }
        
        // Priority 2: Check registry for install location
        var registryInfo = await _installerService.GetDetailedSoftwareInfoAsync(software);
        if (registryInfo != null && !string.IsNullOrEmpty(registryInfo.InstallLocation))
        {
            if (await VerifyInstallationAsync(registryInfo.InstallLocation, software.MainExecutable))
            {
                _logger.LogInformation("Found installation via registry at: {Path}", registryInfo.InstallLocation);
                return new InstallationInfo
                {
                    IsInstalled = true,
                    InstallPath = registryInfo.InstallLocation,
                    DetectionMethod = "Registry",
                    Version = registryInfo.DisplayVersion,
                    RegistryInfo = registryInfo
                };
            }
        }
        
        // Priority 3: Search common directories
        var foundPath = await FindInstallationDirectoryAsync(software.Name);
        if (!string.IsNullOrEmpty(foundPath))
        {
            if (await VerifyInstallationAsync(foundPath, software.MainExecutable))
            {
                _logger.LogInformation("Found installation via directory search at: {Path}", foundPath);
                return new InstallationInfo
                {
                    IsInstalled = true,
                    InstallPath = foundPath,
                    DetectionMethod = "Directory Search",
                    Version = await GetVersionFromDirectory(foundPath, software.MainExecutable)
                };
            }
        }
        
        // Priority 4: Registry only (no directory verification)
        if (registryInfo != null)
        {
            _logger.LogInformation("Found installation via registry only (no directory verification)");
            return new InstallationInfo
            {
                IsInstalled = true,
                InstallPath = registryInfo.InstallLocation,
                DetectionMethod = "Registry Only",
                Version = registryInfo.DisplayVersion,
                RegistryInfo = registryInfo
            };
        }
        
        _logger.LogInformation("No installation found for {Software}", software.Name);
        return null;
    }

    public async Task<string?> FindInstallationDirectoryAsync(string softwareName)
    {
        return await Task.Run(() =>
        {
            // Clean the software name for directory matching
            var cleanName = CleanSoftwareName(softwareName);
            var patterns = GenerateSearchPatterns(cleanName);
            
            foreach (var root in _commonInstallRoots.Distinct())
            {
                if (!Directory.Exists(root))
                    continue;
                
                try
                {
                    // Search for directories matching our patterns
                    var directories = Directory.GetDirectories(root, "*", SearchOption.TopDirectoryOnly);
                    
                    foreach (var dir in directories)
                    {
                        var dirName = Path.GetFileName(dir);
                        
                        // Check if directory name matches any of our patterns
                        foreach (var pattern in patterns)
                        {
                            if (Regex.IsMatch(dirName, pattern, RegexOptions.IgnoreCase))
                            {
                                _logger.LogDebug("Found potential installation directory: {Dir}", dir);
                                return dir;
                            }
                        }
                    }
                }
                catch (UnauthorizedAccessException)
                {
                    // Skip directories we can't access
                    continue;
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Error searching directory: {Root}", root);
                }
            }
            
            return null;
        });
    }

    public async Task<bool> VerifyInstallationAsync(string installPath, string? mainExecutable = null)
    {
        return await Task.Run(() =>
        {
            if (!Directory.Exists(installPath))
                return false;
            
            // If main executable is specified, check for it
            if (!string.IsNullOrEmpty(mainExecutable))
            {
                var exePath = Path.Combine(installPath, mainExecutable);
                if (!File.Exists(exePath))
                {
                    // Try with .exe extension if not included
                    if (!mainExecutable.EndsWith(".exe", StringComparison.OrdinalIgnoreCase))
                    {
                        exePath = Path.Combine(installPath, mainExecutable + ".exe");
                    }
                }
                
                return File.Exists(exePath);
            }
            
            // Otherwise, check if directory has any executables or typical installation files
            try
            {
                var files = Directory.GetFiles(installPath, "*.exe", SearchOption.TopDirectoryOnly);
                if (files.Length > 0)
                    return true;
                
                // Check for other installation indicators
                var hasUninstaller = Directory.GetFiles(installPath, "unins*.exe", SearchOption.AllDirectories).Any();
                var hasDlls = Directory.GetFiles(installPath, "*.dll", SearchOption.TopDirectoryOnly).Any();
                
                return hasUninstaller || hasDlls;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error verifying installation at: {Path}", installPath);
                return false;
            }
        });
    }

    private async Task<string?> GetVersionFromDirectory(string installPath, string? mainExecutable)
    {
        return await Task.Run(() =>
        {
            try
            {
                // Try to get version from main executable
                if (!string.IsNullOrEmpty(mainExecutable))
                {
                    var exePath = Path.Combine(installPath, mainExecutable);
                    if (!exePath.EndsWith(".exe", StringComparison.OrdinalIgnoreCase))
                        exePath += ".exe";
                    
                    if (File.Exists(exePath))
                    {
                        var versionInfo = System.Diagnostics.FileVersionInfo.GetVersionInfo(exePath);
                        if (!string.IsNullOrEmpty(versionInfo.FileVersion))
                            return versionInfo.FileVersion;
                    }
                }
                
                // Try to get version from any exe in the directory
                var exeFiles = Directory.GetFiles(installPath, "*.exe", SearchOption.TopDirectoryOnly);
                foreach (var exe in exeFiles.Take(5)) // Check first 5 executables
                {
                    var versionInfo = System.Diagnostics.FileVersionInfo.GetVersionInfo(exe);
                    if (!string.IsNullOrEmpty(versionInfo.FileVersion))
                        return versionInfo.FileVersion;
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to get version from directory: {Path}", installPath);
            }
            
            return null;
        });
    }

    private string CleanSoftwareName(string name)
    {
        // Remove version numbers and common suffixes
        var cleaned = Regex.Replace(name, @"\s+v?\d+(\.\d+)*.*$", "");
        cleaned = Regex.Replace(cleaned, @"\s+(x64|x86|64-bit|32-bit)$", "", RegexOptions.IgnoreCase);
        cleaned = Regex.Replace(cleaned, @"\s+(Professional|Enterprise|Community|Free|Pro|Trial|Demo)$", "", RegexOptions.IgnoreCase);
        return cleaned.Trim();
    }

    private string[] GenerateSearchPatterns(string cleanName)
    {
        var patterns = new List<string>();
        
        // Exact match
        patterns.Add($"^{Regex.Escape(cleanName)}$");
        
        // With version
        patterns.Add($"^{Regex.Escape(cleanName)}\\s+v?\\d");
        
        // With spaces replaced by various separators
        var variants = new[] { "-", "_", "." };
        foreach (var separator in variants)
        {
            var variant = cleanName.Replace(" ", separator);
            patterns.Add($"^{Regex.Escape(variant)}");
        }
        
        // Remove spaces entirely
        patterns.Add($"^{Regex.Escape(cleanName.Replace(" ", ""))}");
        
        return patterns.ToArray();
    }
}

public class InstallationInfo
{
    public bool IsInstalled { get; set; }
    public string? InstallPath { get; set; }
    public string? Version { get; set; }
    public string DetectionMethod { get; set; } = "Unknown";
    public InstalledSoftwareInfo? RegistryInfo { get; set; }
}