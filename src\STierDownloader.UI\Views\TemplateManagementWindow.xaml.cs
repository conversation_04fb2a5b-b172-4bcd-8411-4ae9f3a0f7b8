using System.Windows;
using System.Windows.Controls;
using Microsoft.Extensions.Logging;
using STierDownloader.Core.Models;
using STierDownloader.Core.Services;

namespace STierDownloader.UI.Views;

public partial class TemplateManagementWindow : Window
{
    private readonly ITemplateManagementService? _templateService;
    private readonly ILogger _logger;
    private List<SoftwareTemplate> _templates = new();
    private SoftwareTemplate? _selectedTemplate;
    private bool _isEditing = false;
    private SoftwareTemplate? _editingTemplate;
    private bool _hasChanges = false;
    
    public TemplateManagementWindow(ITemplateManagementService? templateService, ILogger logger)
    {
        InitializeComponent();
        _templateService = templateService;
        _logger = logger;
        
        LoadTemplates();
        SetupEventHandlers();
    }
    
    private void SetupEventHandlers()
    {
        // Track changes in all input fields
        TemplateNameTextBox.TextChanged += (s, e) => MarkAsChanged();
        DescriptionTextBox.TextChanged += (s, e) => MarkAsChanged();
        SoftwareNameTextBox.TextChanged += (s, e) => MarkAsChanged();
        VersionTextBox.TextChanged += (s, e) => MarkAsChanged();
        TypeComboBox.SelectionChanged += (s, e) => MarkAsChanged();
        InstallPathTextBox.TextChanged += (s, e) => MarkAsChanged();
        MainExecutableTextBox.TextChanged += (s, e) => MarkAsChanged();
        InstallArgumentsTextBox.TextChanged += (s, e) => MarkAsChanged();
        UninstallCommandTextBox.TextChanged += (s, e) => MarkAsChanged();
        RequiresElevationCheckBox.Checked += (s, e) => MarkAsChanged();
        RequiresElevationCheckBox.Unchecked += (s, e) => MarkAsChanged();
        IsZipPackageCheckBox.Checked += (s, e) => MarkAsChanged();
        IsZipPackageCheckBox.Unchecked += (s, e) => MarkAsChanged();
        CleanupAfterInstallCheckBox.Checked += (s, e) => MarkAsChanged();
        CleanupAfterInstallCheckBox.Unchecked += (s, e) => MarkAsChanged();
        FileNamePatternTextBox.TextChanged += (s, e) => MarkAsChanged();
        DefaultCloudPathTextBox.TextChanged += (s, e) => MarkAsChanged();
    }
    
    private void MarkAsChanged()
    {
        if (!_isEditing || _editingTemplate == null)
            return;
            
        _hasChanges = true;
        SaveButton.IsEnabled = true;
        CancelButton.IsEnabled = true;
    }
    
    private async void LoadTemplates()
    {
        if (_templateService == null)
        {
            StatusText.Text = "Template service not available";
            StatusText.Foreground = System.Windows.Media.Brushes.Red;
            return;
        }
        
        try
        {
            _templates = await _templateService.GetTemplatesAsync();
            TemplateListView.ItemsSource = _templates;
            
            if (_templates.Count > 0)
            {
                TemplateListView.SelectedIndex = 0;
            }
            else
            {
                // Clear the details panel when no templates exist
                ClearDetailsPanel();
                StatusText.Text = "No templates found. Click 'Add Template' to create your first template.";
                StatusText.Foreground = System.Windows.Media.Brushes.Blue;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to load templates");
            StatusText.Text = "Failed to load templates";
            StatusText.Foreground = System.Windows.Media.Brushes.Red;
        }
    }
    
    private void ClearDetailsPanel()
    {
        _isEditing = false;
        _editingTemplate = null;
        _hasChanges = false;
        
        TemplateNameTextBox.Text = "";
        DescriptionTextBox.Text = "";
        SoftwareNameTextBox.Text = "";
        VersionTextBox.Text = "";
        TypeComboBox.SelectedIndex = -1;
        InstallPathTextBox.Text = "";
        MainExecutableTextBox.Text = "";
        InstallArgumentsTextBox.Text = "";
        UninstallCommandTextBox.Text = "";
        RequiresElevationCheckBox.IsChecked = false;
        IsZipPackageCheckBox.IsChecked = false;
        CleanupAfterInstallCheckBox.IsChecked = false;
        FileNamePatternTextBox.Text = "";
        DefaultCloudPathTextBox.Text = "";
        
        SaveButton.IsEnabled = false;
        CancelButton.IsEnabled = false;
    }
    
    private void TemplateListView_SelectionChanged(object sender, SelectionChangedEventArgs e)
    {
        if (_hasChanges)
        {
            var result = MessageBox.Show(
                "You have unsaved changes. Do you want to save them?",
                "Unsaved Changes",
                MessageBoxButton.YesNoCancel,
                MessageBoxImage.Question);
                
            if (result == MessageBoxResult.Yes)
            {
                SaveChanges_Click(null, null);
            }
            else if (result == MessageBoxResult.Cancel)
            {
                // Revert selection
                TemplateListView.SelectedItem = _selectedTemplate;
                return;
            }
        }
        
        _selectedTemplate = TemplateListView.SelectedItem as SoftwareTemplate;
        
        if (_selectedTemplate != null && !_isEditing)
        {
            DisplayTemplateDetails(_selectedTemplate);
        }
    }
    
    private void DisplayTemplateDetails(SoftwareTemplate template)
    {
        _isEditing = true;
        _editingTemplate = template;
        _hasChanges = false;
        
        TemplateNameTextBox.Text = template.TemplateName;
        DescriptionTextBox.Text = template.Description ?? "";
        SoftwareNameTextBox.Text = template.SoftwareName ?? "";
        VersionTextBox.Text = template.Version ?? "";
        
        // Set type combo box
        if (template.Type.HasValue)
        {
            foreach (ComboBoxItem item in TypeComboBox.Items)
            {
                if (item.Content.ToString() == template.Type.Value.ToString())
                {
                    TypeComboBox.SelectedItem = item;
                    break;
                }
            }
        }
        else
        {
            TypeComboBox.SelectedIndex = -1;
        }
        
        InstallPathTextBox.Text = template.InstallPath ?? "";
        MainExecutableTextBox.Text = template.MainExecutable ?? "";
        InstallArgumentsTextBox.Text = template.InstallArguments ?? "";
        UninstallCommandTextBox.Text = template.UninstallCommand ?? "";
        RequiresElevationCheckBox.IsChecked = template.RequiresElevation ?? true;
        IsZipPackageCheckBox.IsChecked = template.IsZipPackage ?? false;
        CleanupAfterInstallCheckBox.IsChecked = template.CleanupAfterInstall ?? true;
        FileNamePatternTextBox.Text = template.FileNamePattern ?? "";
        DefaultCloudPathTextBox.Text = template.DefaultCloudPath ?? "";
        
        SaveButton.IsEnabled = false;
        CancelButton.IsEnabled = false;
    }
    
    private void AddTemplate_Click(object sender, RoutedEventArgs e)
    {
        _isEditing = true;
        _editingTemplate = new SoftwareTemplate
        {
            TemplateName = "New Template",
            Type = SoftwareType.Utility,
            RequiresElevation = true
        };
        
        DisplayTemplateDetails(_editingTemplate);
        _hasChanges = true;
        SaveButton.IsEnabled = true;
        CancelButton.IsEnabled = true;
        
        StatusText.Text = "Enter details for the new template";
        StatusText.Foreground = System.Windows.Media.Brushes.Blue;
        
        TemplateNameTextBox.Focus();
        TemplateNameTextBox.SelectAll();
    }
    
    private void DuplicateTemplate_Click(object sender, RoutedEventArgs e)
    {
        if (_selectedTemplate == null) return;
        
        _isEditing = true;
        _editingTemplate = new SoftwareTemplate
        {
            TemplateName = _selectedTemplate.TemplateName + " (Copy)",
            Description = _selectedTemplate.Description,
            SoftwareName = _selectedTemplate.SoftwareName,
            Version = _selectedTemplate.Version,
            Type = _selectedTemplate.Type,
            InstallPath = _selectedTemplate.InstallPath,
            MainExecutable = _selectedTemplate.MainExecutable,
            InstallArguments = _selectedTemplate.InstallArguments,
            UninstallCommand = _selectedTemplate.UninstallCommand,
            RequiresElevation = _selectedTemplate.RequiresElevation,
            IsZipPackage = _selectedTemplate.IsZipPackage,
            CleanupAfterInstall = _selectedTemplate.CleanupAfterInstall,
            FileNamePattern = _selectedTemplate.FileNamePattern,
            DefaultCloudPath = _selectedTemplate.DefaultCloudPath
        };
        
        DisplayTemplateDetails(_editingTemplate);
        _hasChanges = true;
        SaveButton.IsEnabled = true;
        CancelButton.IsEnabled = true;
        
        StatusText.Text = "Duplicated template - make changes and save";
        StatusText.Foreground = System.Windows.Media.Brushes.Blue;
        
        TemplateNameTextBox.Focus();
        TemplateNameTextBox.SelectAll();
    }
    
    private async void DeleteTemplate_Click(object sender, RoutedEventArgs e)
    {
        if (_selectedTemplate == null || _templateService == null) return;
        
        var result = MessageBox.Show(
            $"Are you sure you want to delete the template '{_selectedTemplate.TemplateName}'?",
            "Confirm Delete",
            MessageBoxButton.YesNo,
            MessageBoxImage.Question);
        
        if (result == MessageBoxResult.Yes)
        {
            try
            {
                await _templateService.DeleteTemplateAsync(_selectedTemplate.Id);
                LoadTemplates();
                
                StatusText.Text = $"Deleted template: {_selectedTemplate.TemplateName}";
                StatusText.Foreground = System.Windows.Media.Brushes.Green;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to delete template");
                StatusText.Text = $"Failed to delete: {ex.Message}";
                StatusText.Foreground = System.Windows.Media.Brushes.Red;
            }
        }
    }
    
    private async void SaveChanges_Click(object? sender, RoutedEventArgs? e)
    {
        if (_templateService == null || _editingTemplate == null) return;
        
        // Validate inputs
        if (string.IsNullOrWhiteSpace(TemplateNameTextBox.Text))
        {
            StatusText.Text = "Template name is required";
            StatusText.Foreground = System.Windows.Media.Brushes.Red;
            return;
        }
        
        try
        {
            // Update template properties
            _editingTemplate.TemplateName = TemplateNameTextBox.Text.Trim();
            _editingTemplate.Description = string.IsNullOrWhiteSpace(DescriptionTextBox.Text) 
                ? null : DescriptionTextBox.Text.Trim();
            _editingTemplate.SoftwareName = string.IsNullOrWhiteSpace(SoftwareNameTextBox.Text) 
                ? null : SoftwareNameTextBox.Text.Trim();
            _editingTemplate.Version = string.IsNullOrWhiteSpace(VersionTextBox.Text) 
                ? null : VersionTextBox.Text.Trim();
            
            if (TypeComboBox.SelectedItem is ComboBoxItem item && 
                Enum.TryParse<SoftwareType>(item.Content.ToString(), out var type))
            {
                _editingTemplate.Type = type;
            }
            else
            {
                _editingTemplate.Type = null;
            }
            
            _editingTemplate.InstallPath = string.IsNullOrWhiteSpace(InstallPathTextBox.Text) 
                ? null : InstallPathTextBox.Text.Trim();
            _editingTemplate.MainExecutable = string.IsNullOrWhiteSpace(MainExecutableTextBox.Text) 
                ? null : MainExecutableTextBox.Text.Trim();
            _editingTemplate.InstallArguments = string.IsNullOrWhiteSpace(InstallArgumentsTextBox.Text) 
                ? null : InstallArgumentsTextBox.Text.Trim();
            _editingTemplate.UninstallCommand = string.IsNullOrWhiteSpace(UninstallCommandTextBox.Text) 
                ? null : UninstallCommandTextBox.Text.Trim();
            _editingTemplate.RequiresElevation = RequiresElevationCheckBox.IsChecked;
            _editingTemplate.IsZipPackage = IsZipPackageCheckBox.IsChecked;
            _editingTemplate.CleanupAfterInstall = CleanupAfterInstallCheckBox.IsChecked;
            _editingTemplate.FileNamePattern = string.IsNullOrWhiteSpace(FileNamePatternTextBox.Text) 
                ? null : FileNamePatternTextBox.Text.Trim();
            _editingTemplate.DefaultCloudPath = string.IsNullOrWhiteSpace(DefaultCloudPathTextBox.Text) 
                ? null : DefaultCloudPathTextBox.Text.Trim();
            
            if (_editingTemplate.Id == Guid.Empty)
            {
                // New template
                await _templateService.AddTemplateAsync(_editingTemplate);
                StatusText.Text = $"Added template: {_editingTemplate.TemplateName}";
            }
            else
            {
                // Update existing
                await _templateService.UpdateTemplateAsync(_editingTemplate);
                StatusText.Text = $"Updated template: {_editingTemplate.TemplateName}";
            }
            
            StatusText.Foreground = System.Windows.Media.Brushes.Green;
            
            _hasChanges = false;
            SaveButton.IsEnabled = false;
            CancelButton.IsEnabled = false;
            
            LoadTemplates();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to save template");
            StatusText.Text = $"Failed to save: {ex.Message}";
            StatusText.Foreground = System.Windows.Media.Brushes.Red;
        }
    }
    
    private void CancelEdit_Click(object sender, RoutedEventArgs e)
    {
        _hasChanges = false;
        SaveButton.IsEnabled = false;
        CancelButton.IsEnabled = false;
        
        if (_selectedTemplate != null)
        {
            DisplayTemplateDetails(_selectedTemplate);
        }
        
        StatusText.Text = "";
    }
    
    private void TestPattern_Click(object sender, RoutedEventArgs e)
    {
        var pattern = FileNamePatternTextBox.Text;
        if (string.IsNullOrWhiteSpace(pattern))
        {
            StatusText.Text = "Please enter a pattern to test";
            StatusText.Foreground = System.Windows.Media.Brushes.Orange;
            return;
        }
        
        // Simple test dialog
        var inputDialog = new Window
        {
            Title = "Test File Name Pattern",
            Width = 400,
            Height = 200,
            WindowStartupLocation = WindowStartupLocation.CenterOwner,
            Owner = this
        };
        
        var panel = new StackPanel { Margin = new Thickness(10) };
        var label = new TextBlock 
        { 
            Text = "Enter a filename to test against the pattern:",
            Margin = new Thickness(0, 0, 0, 10)
        };
        var textBox = new TextBox 
        { 
            Text = "example.msi",
            Margin = new Thickness(0, 0, 0, 10)
        };
        var resultLabel = new TextBlock
        {
            Margin = new Thickness(0, 0, 0, 10),
            FontWeight = FontWeights.Bold
        };
        var buttonPanel = new StackPanel 
        { 
            Orientation = Orientation.Horizontal,
            HorizontalAlignment = HorizontalAlignment.Right
        };
        var testButton = new Button 
        { 
            Content = "Test",
            Width = 75,
            Margin = new Thickness(0, 0, 5, 0),
            IsDefault = true
        };
        var closeButton = new Button 
        { 
            Content = "Close",
            Width = 75,
            IsCancel = true
        };
        
        testButton.Click += (s, args) => 
        {
            try
            {
                var matches = System.Text.RegularExpressions.Regex.IsMatch(textBox.Text, pattern);
                resultLabel.Text = matches ? "✓ Pattern matches!" : "✗ Pattern does not match";
                resultLabel.Foreground = matches 
                    ? System.Windows.Media.Brushes.Green 
                    : System.Windows.Media.Brushes.Red;
            }
            catch (Exception ex)
            {
                resultLabel.Text = $"Invalid pattern: {ex.Message}";
                resultLabel.Foreground = System.Windows.Media.Brushes.Red;
            }
        };
        closeButton.Click += (s, args) => { inputDialog.Close(); };
        
        buttonPanel.Children.Add(testButton);
        buttonPanel.Children.Add(closeButton);
        panel.Children.Add(label);
        panel.Children.Add(textBox);
        panel.Children.Add(resultLabel);
        panel.Children.Add(buttonPanel);
        inputDialog.Content = panel;
        
        inputDialog.ShowDialog();
    }
    
    private async void ResetToDefaults_Click(object sender, RoutedEventArgs e)
    {
        if (_templateService == null) return;
        
        var result = MessageBox.Show(
            "This will delete all templates. Are you sure you want to continue?",
            "Clear All Templates",
            MessageBoxButton.YesNo,
            MessageBoxImage.Warning);
        
        if (result == MessageBoxResult.Yes)
        {
            try
            {
                await _templateService.ResetToDefaultTemplatesAsync();
                LoadTemplates();
                
                StatusText.Text = "All templates cleared";
                StatusText.Foreground = System.Windows.Media.Brushes.Green;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to clear templates");
                StatusText.Text = $"Failed to clear: {ex.Message}";
                StatusText.Foreground = System.Windows.Media.Brushes.Red;
            }
        }
    }
    
    private void Close_Click(object sender, RoutedEventArgs e)
    {
        if (_hasChanges)
        {
            var result = MessageBox.Show(
                "You have unsaved changes. Do you want to save them before closing?",
                "Unsaved Changes",
                MessageBoxButton.YesNoCancel,
                MessageBoxImage.Question);
                
            if (result == MessageBoxResult.Yes)
            {
                SaveChanges_Click(null, null);
            }
            else if (result == MessageBoxResult.Cancel)
            {
                return;
            }
        }
        
        DialogResult = true;
        Close();
    }
}