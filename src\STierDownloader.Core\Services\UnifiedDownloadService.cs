using Microsoft.Extensions.Logging;
using STierDownloader.Core.Models;
using System.Security.Cryptography;

namespace STierDownloader.Core.Services;

public interface IUnifiedDownloadService
{
    Task<string> DownloadSoftwareAsync(Software software, IProgress<double>? progress = null, CancellationToken cancellationToken = default);
    Task<bool> VerifyChecksumAsync(string filePath, string expectedChecksum);
}

public class UnifiedDownloadService : IUnifiedDownloadService
{
    private readonly ILogger<UnifiedDownloadService> _logger;
    private readonly IDownloadService _httpDownloadService;
    private readonly ICloudStorageService? _cloudStorageService;
    private readonly HttpClient _httpClient;

    public UnifiedDownloadService(
        ILogger<UnifiedDownloadService> logger,
        IDownloadService httpDownloadService,
        ICloudStorageService? cloudStorageService,
        HttpClient httpClient)
    {
        _logger = logger;
        _httpDownloadService = httpDownloadService;
        _cloudStorageService = cloudStorageService;
        _httpClient = httpClient;
    }

    public async Task<string> DownloadSoftwareAsync(Software software, IProgress<double>? progress = null, CancellationToken cancellationToken = default)
    {
        var downloadPath = GetLocalPath(software);
        
        // Check if already downloaded and valid
        if (File.Exists(downloadPath) && !string.IsNullOrEmpty(software.ChecksumSha256))
        {
            var isValid = await VerifyChecksumAsync(downloadPath, software.ChecksumSha256);
            if (isValid)
            {
                _logger.LogInformation("Software already downloaded and verified: {Name}", software.Name);
                progress?.Report(100);
                return downloadPath;
            }
        }

        switch (software.StorageLocation)
        {
            case StorageLocation.GoogleCloudStorage:
                return await DownloadFromGcsAsync(software, downloadPath, progress, cancellationToken);
                
            case StorageLocation.Http:
                return await DownloadFromHttpAsync(software, downloadPath, progress, cancellationToken);
                
            case StorageLocation.LocalFile:
                return await CopyLocalFileAsync(software, downloadPath, progress, cancellationToken);
                
            case StorageLocation.AzureBlob:
            case StorageLocation.AmazonS3:
                throw new NotImplementedException($"Storage location {software.StorageLocation} is not yet implemented");
                
            default:
                throw new ArgumentException($"Unknown storage location: {software.StorageLocation}");
        }
    }

    private async Task<string> DownloadFromGcsAsync(Software software, string downloadPath, IProgress<double>? progress, CancellationToken cancellationToken)
    {
        if (_cloudStorageService == null)
        {
            throw new InvalidOperationException("Cloud Storage Service is not configured");
        }

        if (string.IsNullOrEmpty(software.CloudObjectName))
        {
            throw new ArgumentException("Cloud object name is required for GCS downloads");
        }

        _logger.LogInformation("Downloading from GCS: {ObjectName}", software.CloudObjectName);

        try
        {
            // If signed URL is required, generate and download via HTTP
            if (software.UseSignedUrl)
            {
                var signedUrl = await _cloudStorageService.GenerateSignedUrlAsync(
                    software.CloudObjectName,
                    TimeSpan.FromHours(software.SignedUrlExpirationHours));
                    
                software.DownloadUrl = signedUrl;
                return await DownloadFromHttpAsync(software, downloadPath, progress, cancellationToken);
            }

            // Direct download from GCS
            var localPath = await _cloudStorageService.DownloadFileAsync(
                software.CloudObjectName,
                downloadPath,
                progress,
                cancellationToken);

            // Verify checksum if provided
            if (!string.IsNullOrEmpty(software.ChecksumSha256))
            {
                var isValid = await VerifyChecksumAsync(localPath, software.ChecksumSha256);
                if (!isValid)
                {
                    File.Delete(localPath);
                    throw new InvalidOperationException("Downloaded file failed checksum verification");
                }
            }

            software.InstallerPath = localPath;
            return localPath;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to download from GCS: {ObjectName}", software.CloudObjectName);
            throw;
        }
    }

    private async Task<string> DownloadFromHttpAsync(Software software, string downloadPath, IProgress<double>? progress, CancellationToken cancellationToken)
    {
        if (string.IsNullOrEmpty(software.DownloadUrl))
        {
            throw new ArgumentException("Download URL is required for HTTP downloads");
        }

        _logger.LogInformation("Downloading from HTTP: {Url}", software.DownloadUrl);

        var localPath = await _httpDownloadService.DownloadFileAsync(
            software.DownloadUrl,
            downloadPath,
            progress,
            cancellationToken);

        // Verify checksum if provided
        if (!string.IsNullOrEmpty(software.ChecksumSha256))
        {
            var isValid = await VerifyChecksumAsync(localPath, software.ChecksumSha256);
            if (!isValid)
            {
                File.Delete(localPath);
                throw new InvalidOperationException("Downloaded file failed checksum verification");
            }
        }

        software.InstallerPath = localPath;
        return localPath;
    }

    private async Task<string> CopyLocalFileAsync(Software software, string downloadPath, IProgress<double>? progress, CancellationToken cancellationToken)
    {
        if (string.IsNullOrEmpty(software.DownloadUrl))
        {
            throw new ArgumentException("Local file path is required");
        }

        _logger.LogInformation("Copying local file: {Path}", software.DownloadUrl);

        if (!File.Exists(software.DownloadUrl))
        {
            throw new FileNotFoundException($"Local file not found: {software.DownloadUrl}");
        }

        Directory.CreateDirectory(Path.GetDirectoryName(downloadPath)!);
        
        await Task.Run(() =>
        {
            File.Copy(software.DownloadUrl, downloadPath, true);
        }, cancellationToken);

        progress?.Report(100);
        software.InstallerPath = downloadPath;
        return downloadPath;
    }

    private string GetLocalPath(Software software)
    {
        var fileName = !string.IsNullOrEmpty(software.CloudObjectName) 
            ? Path.GetFileName(software.CloudObjectName)
            : Path.GetFileName(software.DownloadUrl) ?? $"{software.Name}_{software.Version}.exe";

        var downloadDir = Environment.ExpandEnvironmentVariables("%LOCALAPPDATA%\\STierDownloader\\Downloads");
        Directory.CreateDirectory(downloadDir);
        
        return Path.Combine(downloadDir, fileName);
    }

    public async Task<bool> VerifyChecksumAsync(string filePath, string expectedChecksum)
    {
        if (string.IsNullOrEmpty(expectedChecksum))
            return true;

        try
        {
            using var stream = File.OpenRead(filePath);
            using var sha256 = SHA256.Create();
            var hashBytes = await sha256.ComputeHashAsync(stream);
            var actualChecksum = BitConverter.ToString(hashBytes).Replace("-", "").ToLowerInvariant();
            
            var isValid = actualChecksum.Equals(expectedChecksum, StringComparison.OrdinalIgnoreCase);
            
            if (!isValid)
            {
                _logger.LogWarning("Checksum mismatch for {File}. Expected: {Expected}, Actual: {Actual}", 
                    filePath, expectedChecksum, actualChecksum);
            }

            return isValid;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to verify checksum for {File}", filePath);
            return false;
        }
    }
}