using System.ComponentModel;
using System.Windows;
using STierDownloader.UI.ViewModels;

namespace STierDownloader.UI.Views;

public partial class MainWindow : Window
{
    private readonly MainViewModel _viewModel;
    
    public MainWindow(MainViewModel viewModel)
    {
        InitializeComponent();
        _viewModel = viewModel;
        DataContext = viewModel;
        
        // Handle window closing to ensure proper cleanup
        Closing += OnWindowClosing;
    }
    
    private void OnWindowClosing(object? sender, CancelEventArgs e)
    {
        // Cancel any ongoing operations in the view model
        _viewModel?.CancelAllOperations();
        
        // Ensure the application shuts down completely
        Application.Current.Shutdown();
    }
}