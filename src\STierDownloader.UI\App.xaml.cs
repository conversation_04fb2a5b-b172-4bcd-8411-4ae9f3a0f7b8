using System.Net.Http;
using System.Threading;
using System.Windows;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using STierDownloader.Core.Models;
using STierDownloader.Core.Services;
using STierDownloader.UI.ViewModels;
using STierDownloader.UI.Views;

namespace STierDownloader.UI;

public partial class App : Application
{
    private readonly IHost _host;
    private readonly CancellationTokenSource _shutdownTokenSource = new();

    public App()
    {
        _host = Host.CreateDefaultBuilder()
            .ConfigureAppConfiguration((context, config) =>
            {
                config.AddJsonFile("appsettings.json", optional: false, reloadOnChange: true);
            })
            .ConfigureServices((context, services) =>
            {
                // Configuration
                services.Configure<CloudStorageConfig>(context.Configuration.GetSection("CloudStorage"));
                
                // Core services
                services.AddSingleton<HttpClient>();
                services.AddSingleton<IDownloadService, DownloadService>();
                services.AddSingleton<IZipExtractionService, ZipExtractionService>();
                services.AddSingleton<ITemplateManagementService, TemplateManagementService>();
                services.AddSingleton<IInstallerService>(provider =>
                {
                    var logger = provider.GetRequiredService<ILogger<EnhancedInstallerService>>();
                    var zipService = provider.GetService<IZipExtractionService>();
                    return new EnhancedInstallerService(logger, zipService);
                });
                services.AddSingleton<IEnhancedInstallerService>(provider => 
                    (IEnhancedInstallerService)provider.GetRequiredService<IInstallerService>());
                services.AddSingleton<IInstallationDetectionService, InstallationDetectionService>();
                
                // Cloud Storage services (conditionally)
                var cloudConfig = context.Configuration.GetSection("CloudStorage").Get<CloudStorageConfig>();
                if (cloudConfig != null && !string.IsNullOrEmpty(cloudConfig.BucketName))
                {
                    services.AddSingleton<ICloudStorageService, CloudStorageService>();
                    services.AddSingleton<IUnifiedDownloadService, UnifiedDownloadService>();
                    services.AddSingleton<ICloudSoftwareService, CloudSoftwareService>();
                }
                else
                {
                    // Fallback to HTTP-only download service
                    services.AddSingleton<IUnifiedDownloadService>(provider =>
                    {
                        var logger = provider.GetRequiredService<ILogger<UnifiedDownloadService>>();
                        var downloadService = provider.GetRequiredService<IDownloadService>();
                        var httpClient = provider.GetRequiredService<HttpClient>();
                        return new UnifiedDownloadService(logger, downloadService, null, httpClient);
                    });
                    
                    // No cloud software service without cloud storage
                    services.AddSingleton<ICloudSoftwareService>(provider =>
                    {
                        var logger = provider.GetRequiredService<ILogger<CloudSoftwareService>>();
                        return new CloudSoftwareService(null, logger);
                    });
                }
                
                // ViewModels
                services.AddSingleton<MainViewModel>();
                services.AddTransient<SoftwareViewModel>();
                services.AddTransient<UploadViewModel>(provider =>
                {
                    var cloudService = provider.GetService<ICloudStorageService>();
                    var templateService = provider.GetService<ITemplateManagementService>();
                    var logger = provider.GetRequiredService<ILogger<UploadViewModel>>();
                    var serviceProvider = provider;
                    return new UploadViewModel(cloudService, templateService, logger, serviceProvider);
                });
                services.AddTransient<TemplateManagementWindow>(provider =>
                {
                    var templateService = provider.GetService<ITemplateManagementService>();
                    var logger = provider.GetRequiredService<ILogger<TemplateManagementWindow>>();
                    return new TemplateManagementWindow(templateService, logger);
                });
                
                // Views
                services.AddSingleton<MainWindow>();
            })
            .Build();
    }

    protected override async void OnStartup(StartupEventArgs e)
    {
        await _host.StartAsync();
        
        var mainWindow = _host.Services.GetRequiredService<MainWindow>();
        mainWindow.Show();
        
        base.OnStartup(e);
    }

    protected override async void OnExit(ExitEventArgs e)
    {
        try
        {
            // Signal cancellation to all services
            _shutdownTokenSource.Cancel();
            
            // Give services time to shut down gracefully
            using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(5));
            
            // Dispose of the main window to release resources
            var mainWindow = _host.Services.GetService<MainWindow>();
            mainWindow?.Close();
            
            // Stop the host
            await _host.StopAsync(cts.Token);
            
            // Dispose the host
            _host?.Dispose();
            
            // Force kill any remaining processes if necessary
            Environment.Exit(0);
        }
        catch (Exception ex)
        {
            // Log if possible, but ensure we exit
            try
            {
                var logger = _host?.Services?.GetService<ILogger<App>>();
                logger?.LogError(ex, "Error during application shutdown");
            }
            catch
            {
                // Ignore logging errors during shutdown
            }
            
            Environment.Exit(1);
        }
        finally
        {
            _shutdownTokenSource?.Dispose();
            base.OnExit(e);
        }
    }
}