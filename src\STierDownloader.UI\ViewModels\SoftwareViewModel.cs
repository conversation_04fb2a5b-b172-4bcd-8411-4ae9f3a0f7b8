using System.ComponentModel;
using System.Diagnostics;
using System.IO;
using System.Threading;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Microsoft.Extensions.Logging;
using STierDownloader.Core.Models;
using STierDownloader.Core.Services;

namespace STierDownloader.UI.ViewModels;

public partial class SoftwareViewModel : ObservableObject
{
    private readonly Software _software;
    private readonly IUnifiedDownloadService _downloadService;
    private readonly IInstallerService _installerService;
    private readonly IInstallationDetectionService? _detectionService;
    private readonly ILogger<SoftwareViewModel>? _logger;
    private CancellationTokenSource? _cancellationTokenSource;

    [ObservableProperty]
    [NotifyPropertyChangedFor(nameof(ActionButtonText))]
    [NotifyPropertyChangedFor(nameof(ActionButtonEnabled))]
    [NotifyPropertyChangedFor(nameof(IsProcessing))]
    private bool _isDownloading;

    [ObservableProperty]
    [NotifyPropertyChangedFor(nameof(CanInstall))]
    [NotifyPropertyChangedFor(nameof(ActionButtonText))]
    [NotifyPropertyChangedFor(nameof(ActionButtonEnabled))]
    [NotifyPropertyChangedFor(nameof(IsProcessing))]
    private bool _isInstalling;

    [ObservableProperty]
    private double _downloadProgress;

    [ObservableProperty]
    [NotifyPropertyChangedFor(nameof(CanInstall))]
    [NotifyPropertyChangedFor(nameof(ActionButtonText))]
    [NotifyPropertyChangedFor(nameof(ActionButtonEnabled))]
    [NotifyPropertyChangedFor(nameof(HasUpdate))]
    private bool _isDownloaded;

    [ObservableProperty]
    [NotifyPropertyChangedFor(nameof(CanInstall))]
    [NotifyPropertyChangedFor(nameof(ActionButtonText))]
    [NotifyPropertyChangedFor(nameof(ActionButtonEnabled))]
    [NotifyPropertyChangedFor(nameof(HasUpdate))]
    private bool _isInstalled;

    [ObservableProperty]
    [NotifyPropertyChangedFor(nameof(HasUpdate))]
    private string? _installedVersion;

    [ObservableProperty]
    private string _status = "Not Downloaded";
    
    // Computed property for Install button - only enabled if downloaded but not installed
    public bool CanInstall => IsDownloaded && !IsInstalled && !IsInstalling;
    
    // Check if there's an update available
    public bool HasUpdate => IsInstalled && !string.IsNullOrEmpty(InstalledVersion) && 
                             !string.IsNullOrEmpty(Version) && 
                             CompareVersions(Version, InstalledVersion) > 0;
    
    // Single action button properties
    public string ActionButtonText
    {
        get
        {
            if (IsDownloading) return "Downloading...";
            if (IsInstalling) return "Installing...";
            if (IsInstalled)
            {
                if (HasUpdate) return "Update";
                return "Launch";
            }
            if (IsDownloaded) return "Install";
            return "Download";
        }
    }
    
    public bool ActionButtonEnabled => !IsProcessing;
    public bool IsProcessing => IsDownloading || IsInstalling;

    public string Name => _software.Name;
    public string Version => _software.Version;
    public SoftwareType Type => _software.Type;

    public SoftwareViewModel(
        Software software,
        IUnifiedDownloadService downloadService,
        IInstallerService installerService,
        IInstallationDetectionService? detectionService = null,
        ILogger<SoftwareViewModel>? logger = null)
    {
        _software = software;
        _downloadService = downloadService;
        _installerService = installerService;
        _detectionService = detectionService;
        _logger = logger;
        
        // Check initial status
        Task.Run(async () => await CheckStatusAsync());
    }

    [RelayCommand]
    public async Task DownloadAsync()
    {
        if (IsDownloading || IsDownloaded)
            return;

        IsDownloading = true;
        Status = "Downloading...";
        DownloadProgress = 0;

        try
        {
            var progress = new Progress<double>(p => DownloadProgress = p);
            
            // Use UnifiedDownloadService which handles all storage types
            _software.InstallerPath = await _downloadService.DownloadSoftwareAsync(
                _software,
                progress);

            IsDownloaded = true;
            Status = "Downloaded";
        }
        catch (Exception ex)
        {
            Status = $"Download failed: {ex.Message}";
            IsDownloaded = false;
        }
        finally
        {
            IsDownloading = false;
            DownloadProgress = 0;
        }
    }

    [RelayCommand]
    public async Task InstallAsync()
    {
        if (IsInstalling || !IsDownloaded || IsInstalled)
            return;

        IsInstalling = true;
        Status = "Installing...";

        try
        {
            var success = await _installerService.InstallSoftwareAsync(_software);
            
            if (success)
            {
                IsInstalled = true;
                InstalledVersion = await _installerService.GetInstalledVersionAsync(_software);
                Status = $"Installed (v{InstalledVersion})";
            }
            else
            {
                Status = "Installation failed";
            }
        }
        catch (Exception ex)
        {
            Status = $"Installation failed: {ex.Message}";
        }
        finally
        {
            IsInstalling = false;
        }
    }

    [RelayCommand]
    public async Task UninstallAsync()
    {
        if (IsInstalling || !IsInstalled)
            return;

        IsInstalling = true;
        Status = "Uninstalling...";

        try
        {
            var success = await _installerService.UninstallSoftwareAsync(_software);
            
            if (success)
            {
                IsInstalled = false;
                InstalledVersion = null;
                Status = "Not Installed";
            }
            else
            {
                Status = "Uninstallation failed";
            }
        }
        catch (Exception ex)
        {
            Status = $"Uninstallation failed: {ex.Message}";
        }
        finally
        {
            IsInstalling = false;
        }
    }

    public async Task CheckStatusAsync()
    {
        // Check if already downloaded
        var downloadPath = GetDownloadPath();
        IsDownloaded = File.Exists(downloadPath);
        if (IsDownloaded)
        {
            _software.InstallerPath = downloadPath;
        }
        
        // Check if installed - use detection service for more reliable check
        if (_detectionService != null)
        {
            var installInfo = await _detectionService.DetectInstallationAsync(_software);
            if (installInfo != null && installInfo.IsInstalled)
            {
                IsInstalled = true;
                InstalledVersion = installInfo.Version ?? _software.Version;
                Status = $"Installed (v{InstalledVersion}) - {installInfo.DetectionMethod}";
                
                // Store the detected install path for future use
                if (!string.IsNullOrEmpty(installInfo.InstallPath))
                {
                    _software.ExpectedInstallPath = installInfo.InstallPath;
                }
            }
            else
            {
                IsInstalled = false;
                InstalledVersion = null;
            }
        }
        else
        {
            // Fallback to installer service
            IsInstalled = await _installerService.IsSoftwareInstalledAsync(_software);
            if (IsInstalled)
            {
                InstalledVersion = await _installerService.GetInstalledVersionAsync(_software);
            }
        }
        
        // Update status message
        if (IsInstalled)
        {
            Status = $"Installed (v{InstalledVersion ?? "Unknown"})";
        }
        else if (IsDownloaded)
        {
            Status = "Downloaded";
        }
        else
        {
            Status = "Not Downloaded";
        }
    }
    
    private string GetDownloadPath()
    {
        var fileName = !string.IsNullOrEmpty(_software.CloudObjectName) 
            ? Path.GetFileName(_software.CloudObjectName)
            : Path.GetFileName(_software.DownloadUrl) ?? $"{_software.Name}_{_software.Version}.exe";

        var downloadDir = Environment.ExpandEnvironmentVariables("%LOCALAPPDATA%\\STierDownloader\\Downloads");
        return Path.Combine(downloadDir, fileName);
    }
    
    // Unified action command that handles download, install, launch, or update
    [RelayCommand]
    public async Task ExecuteActionAsync()
    {
        if (IsProcessing) return;
        
        try
        {
            // If installed, either launch or update
            if (IsInstalled)
            {
                if (HasUpdate)
                {
                    // Update process: Download new version then install
                    await UpdateAsync();
                }
                else
                {
                    // Launch the application
                    await LaunchAsync();
                }
            }
            // If downloaded but not installed, install it
            else if (IsDownloaded)
            {
                await InstallAsync();
            }
            // If not downloaded, download and then install
            else
            {
                await DownloadAsync();
                
                // Automatically install after download if successful
                if (IsDownloaded && !IsInstalled)
                {
                    await InstallAsync();
                }
            }
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Failed to execute action for {Software}", Name);
            Status = $"Action failed: {ex.Message}";
        }
    }
    
    // Launch installed software
    private async Task LaunchAsync()
    {
        await Task.Run(() =>
        {
            try
            {
                string? exePath = null;
                
                // Try to find the executable
                if (!string.IsNullOrEmpty(_software.ExpectedInstallPath) && 
                    !string.IsNullOrEmpty(_software.MainExecutable))
                {
                    var fullPath = Path.Combine(_software.ExpectedInstallPath, _software.MainExecutable);
                    if (File.Exists(fullPath))
                    {
                        exePath = fullPath;
                    }
                    else
                    {
                        // Try without .exe extension
                        fullPath = Path.Combine(_software.ExpectedInstallPath, 
                            _software.MainExecutable.Replace(".exe", "", StringComparison.OrdinalIgnoreCase) + ".exe");
                        if (File.Exists(fullPath))
                        {
                            exePath = fullPath;
                        }
                    }
                }
                
                if (string.IsNullOrEmpty(exePath))
                {
                    Status = "Cannot find executable to launch";
                    _logger?.LogWarning("Cannot find executable for {Software} at {Path}", 
                        Name, _software.ExpectedInstallPath);
                    return;
                }
                
                Status = "Launching...";
                var startInfo = new ProcessStartInfo
                {
                    FileName = exePath,
                    UseShellExecute = true,
                    WorkingDirectory = Path.GetDirectoryName(exePath)
                };
                
                Process.Start(startInfo);
                Status = "Launched";
                
                // Reset status after a short delay
                Task.Delay(2000).ContinueWith(_ => 
                {
                    Status = $"Installed (v{InstalledVersion})";
                });
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Failed to launch {Software}", Name);
                Status = $"Launch failed: {ex.Message}";
            }
        });
    }
    
    // Update software (download new version and install)
    private async Task UpdateAsync()
    {
        try
        {
            Status = "Updating...";
            
            // First, download the new version
            IsDownloading = true;
            DownloadProgress = 0;
            
            var progress = new Progress<double>(p => DownloadProgress = p);
            _software.InstallerPath = await _downloadService.DownloadSoftwareAsync(_software, progress);
            
            IsDownloading = false;
            IsDownloaded = true;
            
            // Then install the update
            IsInstalling = true;
            var success = await _installerService.InstallSoftwareAsync(_software);
            
            if (success)
            {
                InstalledVersion = _software.Version;
                Status = $"Updated to v{InstalledVersion}";
                
                // Recheck status to update all properties
                await CheckStatusAsync();
            }
            else
            {
                Status = "Update failed during installation";
            }
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Failed to update {Software}", Name);
            Status = $"Update failed: {ex.Message}";
        }
        finally
        {
            IsDownloading = false;
            IsInstalling = false;
            DownloadProgress = 0;
        }
    }
    
    // Compare version strings (returns positive if v1 > v2)
    private int CompareVersions(string v1, string v2)
    {
        try
        {
            // Try to parse as System.Version objects
            if (System.Version.TryParse(v1, out var version1) && System.Version.TryParse(v2, out var version2))
            {
                return version1.CompareTo(version2);
            }
            
            // Fallback to string comparison
            return string.Compare(v1, v2, StringComparison.OrdinalIgnoreCase);
        }
        catch
        {
            return 0;
        }
    }
    
    /// <summary>
    /// Cancels any ongoing operation (download or installation)
    /// </summary>
    public void CancelOperation()
    {
        try
        {
            _cancellationTokenSource?.Cancel();
            _cancellationTokenSource?.Dispose();
            _cancellationTokenSource = null;
            
            if (IsDownloading)
            {
                IsDownloading = false;
                DownloadProgress = 0;
                Status = "Download cancelled";
            }
            else if (IsInstalling)
            {
                IsInstalling = false;
                Status = "Installation cancelled";
            }
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Error cancelling operation for {Software}", Name);
        }
    }
}