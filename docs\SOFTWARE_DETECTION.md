# Software Detection & Management

## Overview

The S-Tier Downloader uses an enhanced detection system to identify installed software, extract metadata, and manage uninstallation intelligently.

## How Software Detection Works

### 1. Registry Scanning

The app scans three registry locations:
- `HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall` (64-bit programs)
- `HKLM\SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Uninstall` (32-bit programs on 64-bit Windows)
- `HKCU\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall` (User-installed programs)

### 2. Metadata Extraction

For each installed program, we extract:

| Field | Description | Example |
|-------|-------------|---------|
| **DisplayName** | Software name shown in Programs & Features | "Tridium Niagara 4.13" |
| **DisplayVersion** | Version string | "4.13.0.1234" |
| **Publisher** | Software manufacturer | "Tridium Inc." |
| **InstallDate** | Installation date (YYYYMMDD format) | "20240115" |
| **InstallLocation** | Installation directory | "C:\Niagara\Niagara-4.13" |
| **UninstallString** | Command to uninstall | "C:\Program Files\App\uninstall.exe" |
| **QuietUninstallString** | Silent uninstall command | "C:\Program Files\App\uninstall.exe /S" |
| **ProductCode** | MSI Product GUID | "{12345678-1234-1234-1234-123456789012}" |
| **EstimatedSize** | Size in KB | 524288 |
| **VersionMajor/Minor** | Numeric version components | 4, 13 |

### 3. Intelligent Matching

The system uses multiple strategies to match software:

1. **Exact Match**: Display name exactly matches configured name
2. **Contains Match**: Display name contains software name (or vice versa)
3. **Smart Matching**: Removes version numbers and common suffixes before comparing
   - Strips: version numbers, "x64/x86", "Professional/Enterprise", etc.
   - Example: "Niagara 4.13.1 Professional x64" → "Niagara"

### 4. MSI Product Code Extraction

For MSI installers, the app can:
- Extract product code before installation
- Store it for reliable uninstallation
- Build proper uninstall commands automatically

## Uninstallation Process

### Priority Order

1. **Quiet Uninstall String** (if available)
2. **MSI Product Code** (builds: `msiexec /x {GUID} /quiet`)
3. **Regular Uninstall String**
4. **Configured Uninstall Command** (from appsettings.json)

### MSI Uninstallation

For MSI-based software:
```cmd
msiexec.exe /x {ProductCode} /quiet /norestart
```

Options:
- `/x` - Uninstall
- `/quiet` - No UI
- `/norestart` - Don't restart automatically

### EXE Uninstallation

For EXE-based software:
- Uses uninstall string from registry
- Adds silent switches if configured
- Common switches: `/S`, `/SILENT`, `/VERYSILENT`, `/quiet`

## Enhanced Features

### 1. Automatic Product Code Detection

When installing an MSI file, the app:
1. Opens the MSI database
2. Extracts the ProductCode property
3. Stores it for future uninstallation
4. No need to manually configure uninstall commands

### 2. Version Comparison

The app can compare versions to determine if updates are needed:
- Parses version strings (e.g., "4.13.0.1234")
- Compares major.minor.build.revision
- Identifies when installed version differs from configured

### 3. Repair Capability

For MSI installations, the app can repair corrupted installations:
```cmd
msiexec.exe /fa {ProductCode} /quiet
```

### 4. Cache Management

- Caches installed software list for 5 minutes
- Reduces registry scanning overhead
- Auto-refreshes after install/uninstall operations

## Configuration Examples

### Basic Configuration
```json
{
  "Name": "Tridium Niagara 4",
  "Version": "4.13",
  "Type": "TridiumNiagara"
}
```
The app will auto-detect everything else!

### With Explicit Uninstall
```json
{
  "Name": "Custom HVAC Tool",
  "Version": "2.5",
  "UninstallCommand": "C:\\Tools\\uninstall.exe /silent"
}
```

### MSI with Product Code
```json
{
  "Name": "FortiClient VPN",
  "Version": "7.2.0",
  "UninstallCommand": "msiexec /x {12345678-1234-1234-1234-123456789012} /quiet"
}
```

## Troubleshooting Detection Issues

### Software Not Detected

1. **Check Registry Manually**:
   ```cmd
   reg query "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall" /s | findstr /i "YourSoftware"
   ```

2. **Try Different Name Variations**:
   - Full name: "Tridium Niagara Station"
   - Short name: "Niagara"
   - Without version: "Tridium Niagara"

3. **Check 32-bit Registry** (on 64-bit Windows):
   ```cmd
   reg query "HKLM\SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Uninstall"
   ```

### Wrong Version Detected

- Some software stores marketing version vs technical version
- Check both DisplayVersion and file version
- May need custom detection logic for specific software

### Uninstall Fails

1. **Check if silent uninstall is supported**:
   - Not all software supports silent mode
   - May need different switches

2. **Try manual uninstall** to find correct command:
   ```cmd
   msiexec /x {ProductCode} /passive
   ```

3. **Check for dependencies**:
   - Some software requires other components to be uninstalled first

## Best Practices

### For Administrators

1. **Test Detection**: After installing software manually, check if the app detects it
2. **Document Product Codes**: Keep a record of MSI product codes
3. **Use Checksums**: Always generate SHA-256 for installers
4. **Test Silent Install/Uninstall**: Verify commands work silently

### For Configuration

1. **Use Exact Names**: Match the display name in Programs & Features
2. **Include Version**: Helps identify specific installations
3. **MSI Preferred**: MSI installers have better management capabilities
4. **Test First**: Test with one machine before deployment

## Advanced Scenarios

### Multiple Versions

The app can handle multiple versions of the same software:
- Each version tracked separately
- Version-specific uninstall commands
- Side-by-side installations supported

### Portable Software

For software without registry entries:
- Use file existence checks
- Configure custom detection scripts
- Track via configuration files

### Virtual Applications

For App-V or containerized apps:
- May need custom detection logic
- Check virtual registry locations
- Use PowerShell detection scripts

## API Usage

### Get Detailed Software Info
```csharp
var installer = serviceProvider.GetRequiredService<IEnhancedInstallerService>();
var info = await installer.GetDetailedSoftwareInfoAsync(software);

Console.WriteLine($"Installed: {info.DisplayName} v{info.DisplayVersion}");
Console.WriteLine($"Publisher: {info.Publisher}");
Console.WriteLine($"Install Date: {info.InstallDateParsed}");
Console.WriteLine($"Can Uninstall: {info.CanUninstall}");
```

### Extract MSI Properties
```csharp
var properties = await installer.GetMsiPropertiesAsync("installer.msi");
var productCode = properties["ProductCode"];
var manufacturer = properties["Manufacturer"];
```

### List All Installed Software
```csharp
var allSoftware = await installer.GetAllInstalledSoftwareAsync();
foreach (var sw in allSoftware.OrderBy(s => s.DisplayName))
{
    Console.WriteLine($"{sw.DisplayName} - {sw.DisplayVersion}");
}
```