namespace STierDownloader.Core.Models;

public class InstalledSoftwareInfo
{
    public string DisplayName { get; set; } = string.Empty;
    public string? DisplayVersion { get; set; }
    public string? Publisher { get; set; }
    public string? InstallDate { get; set; }
    public string? InstallLocation { get; set; }
    public string? UninstallString { get; set; }
    public string? QuietUninstallString { get; set; }
    public string? ModifyPath { get; set; }
    public string? ProductCode { get; set; }  // MSI Product Code (GUID)
    public string? UpgradeCode { get; set; }  // MSI Upgrade Code
    public long? EstimatedSize { get; set; }  // Size in KB
    public string? Icon { get; set; }
    public string? HelpLink { get; set; }
    public string? URLInfoAbout { get; set; }
    public string? Comments { get; set; }
    public int? VersionMajor { get; set; }
    public int? VersionMinor { get; set; }
    public bool SystemComponent { get; set; }
    public bool NoModify { get; set; }
    public bool NoRemove { get; set; }
    public bool NoRepair { get; set; }
    public string RegistryPath { get; set; } = string.Empty;
    public string RegistryKey { get; set; } = string.Empty;
    
    // Additional metadata for better matching
    public bool IsWindowsInstaller { get; set; }  // True if MSI-based
    public DateTime? InstallDateParsed { get; set; }
    public Version? ParsedVersion { get; set; }
    
    // Computed properties
    public bool CanUninstall => !NoRemove && !string.IsNullOrEmpty(UninstallString);
    public bool CanModify => !NoModify && !string.IsNullOrEmpty(ModifyPath);
    public bool CanRepair => !NoRepair && IsWindowsInstaller;
    
    public string GetEffectiveUninstallCommand()
    {
        // Prefer quiet uninstall if available
        if (!string.IsNullOrEmpty(QuietUninstallString))
            return QuietUninstallString;
            
        // For MSI, build quiet uninstall command
        if (IsWindowsInstaller && !string.IsNullOrEmpty(ProductCode))
            return $"msiexec.exe /x {ProductCode} /quiet /norestart";
            
        // Fallback to regular uninstall string
        return UninstallString ?? string.Empty;
    }
    
    public bool MatchesSoftware(Software software)
    {
        // If MSI product code is available, use it for exact matching
        if (!string.IsNullOrEmpty(software.MsiProductCode) && 
            !string.IsNullOrEmpty(ProductCode) &&
            software.MsiProductCode.Equals(ProductCode, StringComparison.OrdinalIgnoreCase))
        {
            return true;
        }
        
        // Try exact match first
        if (DisplayName.Equals(software.Name, StringComparison.OrdinalIgnoreCase))
            return true;
            
        // Try contains match
        if (DisplayName.Contains(software.Name, StringComparison.OrdinalIgnoreCase))
            return true;
            
        // Try reverse contains (software name contains display name)
        if (software.Name.Contains(DisplayName, StringComparison.OrdinalIgnoreCase))
            return true;
            
        // Advanced matching: remove common suffixes and compare
        var cleanDisplayName = CleanSoftwareName(DisplayName);
        var cleanSoftwareName = CleanSoftwareName(software.Name);
        
        return cleanDisplayName.Equals(cleanSoftwareName, StringComparison.OrdinalIgnoreCase);
    }
    
    private static string CleanSoftwareName(string name)
    {
        // Remove common version patterns and suffixes
        var cleaned = System.Text.RegularExpressions.Regex.Replace(name, @"\s+v?\d+(\.\d+)*.*$", "");
        cleaned = System.Text.RegularExpressions.Regex.Replace(cleaned, @"\s+(x64|x86|64-bit|32-bit)$", "", System.Text.RegularExpressions.RegexOptions.IgnoreCase);
        cleaned = System.Text.RegularExpressions.Regex.Replace(cleaned, @"\s+(Professional|Enterprise|Community|Free|Pro|Trial|Demo)$", "", System.Text.RegularExpressions.RegexOptions.IgnoreCase);
        return cleaned.Trim();
    }
}