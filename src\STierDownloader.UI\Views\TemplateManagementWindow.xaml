<Window x:Class="STierDownloader.UI.Views.TemplateManagementWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:converters="clr-namespace:STierDownloader.UI.Converters"
        mc:Ignorable="d"
        Title="Manage Software Templates"
        Height="700" Width="1100"
        WindowStartupLocation="CenterOwner">
    
    <Window.Resources>
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
        <converters:InverseBooleanConverter x:Key="InverseBooleanConverter"/>
        <converters:NullToBooleanConverter x:Key="NullToBooleanConverter"/>
    </Window.Resources>
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="#FF673AB7" Padding="15">
            <StackPanel>
                <TextBlock Text="Software Template Management" 
                          FontSize="20" FontWeight="Bold" Foreground="White"/>
                <TextBlock Text="Create and manage templates for quick software metadata entry" 
                          FontSize="12" Foreground="#E1BEE7" Margin="0,3,0,0"/>
            </StackPanel>
        </Border>

        <!-- Content -->
        <Grid Grid.Row="1" Margin="10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="400"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- Template List -->
            <GroupBox Grid.Column="0" Header="Templates" Margin="0,0,5,0">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>
                    
                    <!-- Toolbar -->
                    <ToolBar Grid.Row="0">
                        <Button Content="➕ Add Template" 
                               Click="AddTemplate_Click"
                               Padding="8,4"/>
                        <Separator/>
                        <Button Content="📋 Duplicate" 
                               Click="DuplicateTemplate_Click"
                               IsEnabled="{Binding ElementName=TemplateListView, Path=SelectedItem, Converter={StaticResource NullToBooleanConverter}}"
                               Padding="8,4"/>
                        <Button Content="🗑️ Delete" 
                               Click="DeleteTemplate_Click"
                               IsEnabled="{Binding ElementName=TemplateListView, Path=SelectedItem, Converter={StaticResource NullToBooleanConverter}}"
                               Padding="8,4"/>
                        <Separator/>
                        <Button Content="🗑️ Clear All" 
                               Click="ResetToDefaults_Click"
                               ToolTip="Delete all templates"
                               Padding="8,4"/>
                    </ToolBar>
                    
                    <!-- Template List -->
                    <ListView Grid.Row="1" 
                             Name="TemplateListView"
                             SelectionChanged="TemplateListView_SelectionChanged">
                        <ListView.ItemTemplate>
                            <DataTemplate>
                                <Border BorderBrush="#E0E0E0" BorderThickness="0,0,0,1" Padding="10,8">
                                    <StackPanel>
                                        <TextBlock Text="{Binding TemplateName}" 
                                                  FontWeight="Bold" FontSize="14"/>
                                        <TextBlock Text="{Binding Description}" 
                                                  FontStyle="Italic" Foreground="#666" 
                                                  TextWrapping="Wrap" Margin="0,2,0,0"/>
                                        <StackPanel Orientation="Horizontal" Margin="0,4,0,0">
                                            <TextBlock Text="Type: " Foreground="#888"/>
                                            <TextBlock Text="{Binding Type}" Foreground="#666"/>
                                            <TextBlock Text=" | " Foreground="#DDD" Margin="5,0"/>
                                        </StackPanel>
                                    </StackPanel>
                                </Border>
                            </DataTemplate>
                        </ListView.ItemTemplate>
                    </ListView>
                </Grid>
            </GroupBox>

            <!-- Template Details Panel -->
            <GroupBox Grid.Column="1" Header="Template Details" Margin="5,0,0,0">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel Name="DetailsPanel" Margin="10">
                        <!-- Template Properties -->
                        <TextBlock Text="Template Name:" FontWeight="Bold" Margin="0,5"/>
                        <TextBox Name="TemplateNameTextBox" Margin="0,2,0,10"/>
                        
                        <TextBlock Text="Description:" FontWeight="Bold"/>
                        <TextBox Name="DescriptionTextBox" Margin="0,2,0,10"
                                TextWrapping="Wrap" AcceptsReturn="True" Height="50"/>
                        
                        <!-- Software Properties Section -->
                        <Border BorderBrush="#E0E0E0" BorderThickness="0,1,0,0" Margin="0,10,0,5" Padding="0,10,0,0">
                            <TextBlock Text="Default Software Properties" FontWeight="Bold" FontSize="14" Foreground="#673AB7"/>
                        </Border>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="10"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <!-- Left Column -->
                            <StackPanel Grid.Column="0">
                                <TextBlock Text="Software Name:" FontWeight="Bold"/>
                                <TextBox Name="SoftwareNameTextBox" Margin="0,2,0,10"/>
                                
                                <TextBlock Text="Version:" FontWeight="Bold"/>
                                <TextBox Name="VersionTextBox" Margin="0,2,0,10"/>
                                
                                <TextBlock Text="Software Type:" FontWeight="Bold"/>
                                <ComboBox Name="TypeComboBox" Margin="0,2,0,10">
                                    <ComboBoxItem>Utility</ComboBoxItem>
                                    <ComboBoxItem>TridiumNiagara</ComboBoxItem>
                                    <ComboBoxItem>HvacProgrammingTool</ComboBoxItem>
                                    <ComboBoxItem>VpnClient</ComboBoxItem>
                                    <ComboBoxItem>Driver</ComboBoxItem>
                                    <ComboBoxItem>Framework</ComboBoxItem>
                                </ComboBox>
                                
                                <TextBlock Text="Installation Path:" FontWeight="Bold"/>
                                <TextBox Name="InstallPathTextBox" Margin="0,2,0,10"/>
                                
                                <TextBlock Text="Main Executable:" FontWeight="Bold"/>
                                <TextBox Name="MainExecutableTextBox" Margin="0,2,0,10"/>
                            </StackPanel>
                            
                            <!-- Right Column -->
                            <StackPanel Grid.Column="2">
                                <TextBlock Text="Install Arguments:" FontWeight="Bold"/>
                                <TextBox Name="InstallArgumentsTextBox" Margin="0,2,0,10"/>
                                
                                <TextBlock Text="Uninstall Command:" FontWeight="Bold"/>
                                <TextBox Name="UninstallCommandTextBox" Margin="0,2,0,10"/>
                                
                                <CheckBox Name="RequiresElevationCheckBox" Content="Requires Admin/Elevation" 
                                         Margin="0,10,0,5" FontWeight="Bold"/>
                                <CheckBox Name="IsZipPackageCheckBox" Content="Is ZIP Package" 
                                         Margin="0,5"/>
                                <CheckBox Name="CleanupAfterInstallCheckBox" Content="Cleanup After Install" 
                                         Margin="0,5" IsEnabled="{Binding ElementName=IsZipPackageCheckBox, Path=IsChecked}"/>
                            </StackPanel>
                        </Grid>
                        
                        <!-- Advanced Section -->
                        <Border BorderBrush="#E0E0E0" BorderThickness="0,1,0,0" Margin="0,15,0,5" Padding="0,10,0,0">
                            <TextBlock Text="Advanced Properties" FontWeight="Bold" FontSize="14" Foreground="#673AB7"/>
                        </Border>
                        
                        <TextBlock Text="File Name Pattern (Regex):" FontWeight="Bold"/>
                        <TextBox Name="FileNamePatternTextBox" Margin="0,2,0,5"
                                ToolTip="Regular expression to match file names"/>
                        <TextBlock Text="Example: \.msi$ for MSI files" FontStyle="Italic" Foreground="#888" Margin="0,0,0,10"/>
                        
                        <TextBlock Text="Default Cloud Path:" FontWeight="Bold"/>
                        <TextBox Name="DefaultCloudPathTextBox" Margin="0,2,0,5"/>
                        <TextBlock Text="Example: software/niagara/vykon/" FontStyle="Italic" Foreground="#888" Margin="0,0,0,10"/>
                        
                        <!-- Action Buttons -->
                        <Border BorderBrush="#E0E0E0" BorderThickness="0,1,0,0" Margin="0,15,0,10"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Button Grid.Column="0" Content="Save Changes" 
                                   Click="SaveChanges_Click"
                                   Margin="0,0,5,0" Padding="10,8"
                                   IsEnabled="False"
                                   Name="SaveButton"
                                   Background="#4CAF50" Foreground="White"/>
                            <Button Grid.Column="1" Content="Cancel" 
                                   Click="CancelEdit_Click"
                                   Margin="5,0" Padding="10,8"
                                   IsEnabled="False"
                                   Name="CancelButton"/>
                            <Button Grid.Column="2" Content="Test Pattern" 
                                   Click="TestPattern_Click"
                                   Margin="5,0,0,0" Padding="10,8"
                                   ToolTip="Test the file name pattern"/>
                        </Grid>
                        
                        <TextBlock Name="StatusText" 
                                  Margin="0,10,0,0"
                                  TextWrapping="Wrap"
                                  Foreground="Green"
                                  FontStyle="Italic"/>
                    </StackPanel>
                </ScrollViewer>
            </GroupBox>
        </Grid>

        <!-- Footer -->
        <Border Grid.Row="2" Background="#F5F5F5" Padding="10">
            <Grid>
                <TextBlock HorizontalAlignment="Left" VerticalAlignment="Center">
                    <Run Text="Templates help pre-fill upload forms. Select a template when uploading to auto-populate metadata." FontStyle="Italic" Foreground="#666"/>
                </TextBlock>
                <Button HorizontalAlignment="Right" 
                       Content="Close" 
                       Click="Close_Click"
                       Padding="20,8"
                       IsDefault="True"/>
            </Grid>
        </Border>
    </Grid>
</Window>