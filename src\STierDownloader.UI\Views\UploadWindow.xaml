<Window x:Class="STierDownloader.UI.Views.UploadWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:converters="clr-namespace:STierDownloader.UI.Converters"
        mc:Ignorable="d"
        Title="Upload Software to Cloud Storage - Admin"
        Height="700" Width="900"
        WindowStartupLocation="CenterScreen">
    
    <Window.Resources>
        <converters:InverseBooleanConverter x:Key="InverseBooleanConverter"/>
        <converters:StringToBooleanConverter x:Key="StringToBooleanConverter"/>
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
    </Window.Resources>
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="#FF1E88E5" Padding="20">
            <StackPanel>
                <TextBlock Text="Admin: Upload Software" 
                          FontSize="24" FontWeight="Bold" Foreground="White"/>
                <TextBlock Text="Upload installers to Google Cloud Storage" 
                          FontSize="14" Foreground="#E3F2FD" Margin="0,5,0,0"/>
            </StackPanel>
        </Border>

        <!-- Main Content -->
        <ScrollViewer Grid.Row="1" Padding="20">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="2*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- Left Panel - Upload Form -->
                <StackPanel Grid.Column="0" Margin="0,0,20,0">
                    <GroupBox Header="File Selection" Padding="10">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <TextBox Grid.Column="0" Text="{Binding LocalFilePath}" 
                                    IsReadOnly="True" VerticalAlignment="Center"/>
                            <Button Grid.Column="1" Content="Browse..." 
                                   Command="{Binding BrowseFileCommand}"
                                   Margin="5,0,0,0" Padding="10,5"/>
                        </Grid>
                    </GroupBox>

                    <GroupBox Header="Software Information" Padding="10" Margin="0,10,0,0">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="120"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <TextBlock Grid.Row="0" Grid.Column="0" Text="Software Name:" 
                                      VerticalAlignment="Center" Margin="0,5"/>
                            <TextBox Grid.Row="0" Grid.Column="1" 
                                    Text="{Binding SoftwareName, UpdateSourceTrigger=PropertyChanged}" 
                                    Margin="0,5"/>

                            <TextBlock Grid.Row="1" Grid.Column="0" Text="Version:" 
                                      VerticalAlignment="Center" Margin="0,5"/>
                            <TextBox Grid.Row="1" Grid.Column="1" 
                                    Text="{Binding Version, UpdateSourceTrigger=PropertyChanged}" 
                                    Margin="0,5"/>

                            <TextBlock Grid.Row="2" Grid.Column="0" Text="Type:" 
                                      VerticalAlignment="Center" Margin="0,5"/>
                            <ComboBox Grid.Row="2" Grid.Column="1" 
                                     ItemsSource="{Binding SoftwareTypes}"
                                     SelectedItem="{Binding SelectedType}" 
                                     Margin="0,5"/>

                            <TextBlock Grid.Row="3" Grid.Column="0" Text="Cloud Path:" 
                                      VerticalAlignment="Center" Margin="0,5"/>
                            <Grid Grid.Row="3" Grid.Column="1">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <TextBox Grid.Column="0" Text="{Binding CloudPath, UpdateSourceTrigger=PropertyChanged}" 
                                        Margin="0,5"/>
                                <Button Grid.Column="1" Content="📋" ToolTip="Copy path"
                                       Command="{Binding CopyCloudPathCommand}"
                                       Margin="5,5,0,5" Padding="5,2"/>
                            </Grid>
                        </Grid>
                    </GroupBox>

                    <GroupBox Header="Installation Settings" Padding="10" Margin="0,10,0,0">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="120"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            
                            <!-- Template Selector -->
                            <TextBlock Grid.Row="0" Grid.Column="0" Text="Template:" 
                                      VerticalAlignment="Center" Margin="0,5"
                                      ToolTip="Select a template to pre-fill metadata"/>
                            <Grid Grid.Row="0" Grid.Column="1">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <ComboBox Grid.Column="0" 
                                         ItemsSource="{Binding AvailableTemplates}"
                                         SelectedItem="{Binding SelectedTemplate}"
                                         DisplayMemberPath="TemplateName"
                                         Margin="0,5">
                                    <ComboBox.ItemContainerStyle>
                                        <Style TargetType="ComboBoxItem">
                                            <Setter Property="ToolTip" Value="{Binding Description}"/>
                                        </Style>
                                    </ComboBox.ItemContainerStyle>
                                </ComboBox>
                                <Button Grid.Column="1" 
                                       Content="⚙" 
                                       ToolTip="Manage Templates"
                                       Command="{Binding OpenTemplateManagementCommand}"
                                       Margin="5,5,0,5" 
                                       Padding="8,2"/>
                            </Grid>

                            <TextBlock Grid.Row="1" Grid.Column="0" Text="Install Path:" 
                                      VerticalAlignment="Center" Margin="0,5"
                                      ToolTip="Expected installation directory (e.g., C:\Program Files\MyApp)"/>
                            <Grid Grid.Row="1" Grid.Column="1">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <TextBox Grid.Column="0" 
                                        Text="{Binding ExpectedInstallPath}" 
                                        ToolTip="e.g., C:\Program Files\MyApp"
                                        Margin="0,5"/>
                                <Button Grid.Column="1" 
                                       Content="Browse EXE..." 
                                       Command="{Binding BrowseInstalledExeCommand}"
                                       ToolTip="Browse to the installed executable to auto-fill paths"
                                       Margin="5,5,0,5" 
                                       Padding="10,5"/>
                            </Grid>

                            <TextBlock Grid.Row="2" Grid.Column="0" Text="Main Executable:" 
                                      VerticalAlignment="Center" Margin="0,5"
                                      ToolTip="Main executable file to verify installation"/>
                            <TextBox Grid.Row="2" Grid.Column="1" 
                                    Text="{Binding MainExecutable}" 
                                    ToolTip="e.g., MyApp.exe or just MyApp"
                                    Margin="0,5"/>

                            <TextBlock Grid.Row="3" Grid.Column="0" Text="Install Args:" 
                                      VerticalAlignment="Center" Margin="0,5"/>
                            <TextBox Grid.Row="3" Grid.Column="1" 
                                    Text="{Binding InstallArguments}" 
                                    ToolTip="e.g., /S /v/qn for EXE or /quiet for MSI"
                                    Margin="0,5"/>

                            <TextBlock Grid.Row="4" Grid.Column="0" Text="Uninstall Cmd:" 
                                      VerticalAlignment="Center" Margin="0,5"/>
                            <TextBox Grid.Row="4" Grid.Column="1" 
                                    Text="{Binding UninstallCommand}" 
                                    ToolTip="Command to uninstall the software"
                                    Margin="0,5"/>

                            <TextBlock Grid.Row="5" Grid.Column="0" Text="SHA-256:" 
                                      VerticalAlignment="Center" Margin="0,5"/>
                            <TextBox Grid.Row="5" Grid.Column="1" 
                                    Text="{Binding ChecksumSha256}" 
                                    IsReadOnly="True"
                                    FontFamily="Consolas"
                                    Margin="0,5"/>

                            <TextBlock Grid.Row="6" Grid.Column="0" Text="Options:" 
                                      VerticalAlignment="Center" Margin="0,5"/>
                            <StackPanel Grid.Row="6" Grid.Column="1" Orientation="Horizontal" Margin="0,5">
                                <CheckBox Content="Requires Admin" 
                                         IsChecked="{Binding RequiresElevation}" 
                                         Margin="0,0,20,0"/>
                                <CheckBox Content="Generate Checksum" 
                                         IsChecked="{Binding GenerateChecksum}" 
                                         Margin="0,0,20,0"/>
                                <CheckBox Content="Cleanup After Install" 
                                         IsChecked="{Binding CleanupAfterInstall}"
                                         Visibility="{Binding IsZipPackage, Converter={StaticResource BooleanToVisibilityConverter}}"
                                         Margin="0,0,20,0"/>
                                <CheckBox Content="Use Signed URLs" 
                                         IsChecked="{Binding UseSignedUrl}"/>
                                <TextBox Text="{Binding SignedUrlHours}" 
                                        Width="40" Margin="5,0,0,0"
                                        IsEnabled="{Binding UseSignedUrl}"/>
                                <TextBlock Text="hours" VerticalAlignment="Center" Margin="5,0,0,0"/>
                            </StackPanel>
                        </Grid>
                    </GroupBox>

                    <!-- Upload Button and Progress -->
                    <Grid Margin="0,20,0,0">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        
                        <Button Grid.Row="0" Content="Upload to Cloud Storage" 
                               Command="{Binding UploadCommand}"
                               IsEnabled="{Binding IsUploading, Converter={StaticResource InverseBooleanConverter}}"
                               Height="40" FontSize="16" FontWeight="Bold"
                               Background="#FF4CAF50" Foreground="White"/>
                        
                        <!-- Progress Bar with Percentage -->
                        <Grid Grid.Row="1" Margin="0,10,0,0"
                              Visibility="{Binding IsUploading, Converter={StaticResource BooleanToVisibilityConverter}}">
                            <ProgressBar Height="24" Value="{Binding UploadProgress}"/>
                            <TextBlock HorizontalAlignment="Center" VerticalAlignment="Center"
                                      FontWeight="Bold" Foreground="White">
                                <TextBlock.Effect>
                                    <DropShadowEffect ShadowDepth="1" BlurRadius="2" Opacity="0.5"/>
                                </TextBlock.Effect>
                                <TextBlock.Text>
                                    <MultiBinding StringFormat="{}{0:F1}%">
                                        <Binding Path="UploadProgress"/>
                                    </MultiBinding>
                                </TextBlock.Text>
                            </TextBlock>
                        </Grid>
                        
                        <!-- Upload Metrics -->
                        <Grid Grid.Row="2" Margin="0,5,0,0"
                              Visibility="{Binding IsUploading, Converter={StaticResource BooleanToVisibilityConverter}}">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <!-- Bytes Uploaded -->
                            <StackPanel Grid.Column="0" HorizontalAlignment="Center">
                                <TextBlock Text="Uploaded" FontSize="10" Foreground="Gray"/>
                                <TextBlock Text="{Binding BytesUploaded}" FontWeight="Bold" FontSize="11"/>
                            </StackPanel>
                            
                            <!-- Speed -->
                            <StackPanel Grid.Column="1" HorizontalAlignment="Center">
                                <TextBlock Text="Speed" FontSize="10" Foreground="Gray"/>
                                <TextBlock Text="{Binding UploadSpeed}" FontWeight="Bold" FontSize="11" Foreground="Green"/>
                            </StackPanel>
                            
                            <!-- Time Remaining -->
                            <StackPanel Grid.Column="2" HorizontalAlignment="Center">
                                <TextBlock Text="Remaining" FontSize="10" Foreground="Gray"/>
                                <TextBlock Text="{Binding TimeRemaining}" FontWeight="Bold" FontSize="11" Foreground="Blue"/>
                            </StackPanel>
                            
                            <!-- Time Elapsed -->
                            <StackPanel Grid.Column="3" HorizontalAlignment="Center">
                                <TextBlock Text="Elapsed" FontSize="10" Foreground="Gray"/>
                                <TextBlock Text="{Binding TimeElapsed}" FontWeight="Bold" FontSize="11"/>
                            </StackPanel>
                        </Grid>
                    </Grid>
                </StackPanel>

                <!-- Right Panel - Recent Uploads and Tools -->
                <StackPanel Grid.Column="1">
                    <GroupBox Header="Quick Actions" Padding="10">
                        <StackPanel>
                            <Button Content="List Bucket Contents" 
                                   Command="{Binding ListBucketContentsCommand}"
                                   Margin="0,0,0,5" Padding="5"/>
                            <Button Content="Generate Signed URL" 
                                   Command="{Binding GenerateSignedUrlCommand}"
                                   IsEnabled="{Binding CloudPath, Converter={StaticResource StringToBooleanConverter}}"
                                   Margin="0,0,0,5" Padding="5"/>
                        </StackPanel>
                    </GroupBox>

                    <GroupBox Header="Recent Uploads / Bucket Contents" Padding="10" Margin="0,10,0,0">
                        <ListBox ItemsSource="{Binding RecentUploads}" 
                                Height="300"
                                ScrollViewer.HorizontalScrollBarVisibility="Auto">
                            <ListBox.ItemTemplate>
                                <DataTemplate>
                                    <TextBlock Text="{Binding}" 
                                             FontFamily="Consolas" 
                                             FontSize="11"/>
                                </DataTemplate>
                            </ListBox.ItemTemplate>
                        </ListBox>
                    </GroupBox>

                    <GroupBox Header="Tips" Padding="10" Margin="0,10,0,0">
                        <TextBlock TextWrapping="Wrap" FontSize="11">
                            • Files are uploaded to GCS bucket
                            <LineBreak/>
                            • Configuration JSON is copied to clipboard
                            <LineBreak/>
                            • Add the JSON to appsettings.json
                            <LineBreak/>
                            • Use folders: software/category/file.exe
                            <LineBreak/>
                            • Generate checksums for verification
                        </TextBlock>
                    </GroupBox>
                </StackPanel>
            </Grid>
        </ScrollViewer>

        <!-- Status Bar -->
        <StatusBar Grid.Row="2">
            <StatusBarItem>
                <TextBlock Text="{Binding StatusMessage}"/>
            </StatusBarItem>
        </StatusBar>
    </Grid>
</Window>