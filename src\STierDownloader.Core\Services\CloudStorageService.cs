using Google.Apis.Auth.OAuth2;
using Google.Cloud.Storage.V1;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using STierDownloader.Core.Models;
using System.Diagnostics;
using System.Security.Cryptography;
using Object = Google.Apis.Storage.v1.Data.Object;

namespace STierDownloader.Core.Services;

public interface ICloudStorageService
{
    Task<Stream> DownloadFileStreamAsync(string objectName, CancellationToken cancellationToken = default);
    Task<string> DownloadFileAsync(string objectName, string localPath, IProgress<double>? progress = null, CancellationToken cancellationToken = default);
    Task<string> UploadFileAsync(string localPath, string objectName, CancellationToken cancellationToken = default);
    Task<bool> FileExistsAsync(string objectName, CancellationToken cancellationToken = default);
    Task<IEnumerable<string>> ListFilesAsync(string prefix = "", CancellationToken cancellationToken = default);
    Task DeleteFileAsync(string objectName, CancellationToken cancellationToken = default);
    Task<string> GenerateSignedUrlAsync(string objectName, TimeSpan expiration);
    Task<StorageFileInfo> GetFileInfoAsync(string objectName, CancellationToken cancellationToken = default);
    Task<Dictionary<string, string>> GetObjectMetadataAsync(string objectName, CancellationToken cancellationToken = default);
    Task UpdateObjectMetadataAsync(string objectName, Dictionary<string, string> metadata, CancellationToken cancellationToken = default);
    Task<string> UploadFileWithMetadataAsync(string localPath, string objectName, Dictionary<string, string> metadata, CancellationToken cancellationToken = default);
    Task<string> UploadFileWithProgressAsync(string localPath, string objectName, Dictionary<string, string>? metadata, IProgress<UploadProgress>? progress, CancellationToken cancellationToken = default);
}

public class CloudStorageService : ICloudStorageService
{
    private readonly ILogger<CloudStorageService> _logger;
    private readonly CloudStorageConfig _config;
    private readonly StorageClient _storageClient;
    private readonly string _bucketName;
    private GoogleCredential? _credential;

    public CloudStorageService(ILogger<CloudStorageService> logger, IOptions<CloudStorageConfig> config)
    {
        _logger = logger;
        _config = config.Value;
        _bucketName = _config.BucketName;

        // Initialize Google Cloud Storage client
        if (_config.UseApplicationDefaultCredentials)
        {
            _credential = GoogleCredential.GetApplicationDefault();
            _storageClient = StorageClient.Create(_credential);
        }
        else if (!string.IsNullOrEmpty(_config.CredentialsPath))
        {
            _credential = GoogleCredential.FromFile(_config.CredentialsPath);
            _storageClient = StorageClient.Create(_credential);
        }
        else
        {
            throw new InvalidOperationException("No valid credentials configured for Google Cloud Storage");
        }

        _logger.LogInformation("Cloud Storage Service initialized for bucket: {BucketName}", _bucketName);
    }

    public async Task<Stream> DownloadFileStreamAsync(string objectName, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Downloading stream for object: {ObjectName}", objectName);
            var memoryStream = new MemoryStream();
            await _storageClient.DownloadObjectAsync(_bucketName, objectName, memoryStream, cancellationToken: cancellationToken);
            memoryStream.Position = 0;
            return memoryStream;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to download stream for object: {ObjectName}", objectName);
            throw;
        }
    }

    public async Task<string> DownloadFileAsync(string objectName, string localPath, IProgress<double>? progress = null, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Downloading object: {ObjectName} to {LocalPath}", objectName, localPath);

            // Ensure directory exists
            var directory = Path.GetDirectoryName(localPath);
            if (!string.IsNullOrEmpty(directory))
            {
                Directory.CreateDirectory(directory);
            }

            // Get file info first
            var objectInfo = await GetFileInfoAsync(objectName, cancellationToken);

            // Check if file is already cached
            if (_config.EnableCaching && File.Exists(localPath))
            {
                var localFileInfo = new FileInfo(localPath);
                
                // Compare checksums if available
                if (objectInfo.Md5Hash != null)
                {
                    using var stream = File.OpenRead(localPath);
                    using var md5 = MD5.Create();
                    var localHash = Convert.ToBase64String(await md5.ComputeHashAsync(stream, cancellationToken));
                    
                    if (localHash == objectInfo.Md5Hash)
                    {
                        _logger.LogInformation("File already cached and up to date: {LocalPath}", localPath);
                        progress?.Report(100);
                        return localPath;
                    }
                }
            }

            // Download with progress tracking
            using var fileStream = File.Create(localPath);
            
            // Simple progress reporting based on file size
            if (progress != null && objectInfo.Size.HasValue)
            {
                var buffer = new byte[81920]; // 80KB buffer
                var totalBytes = (long)objectInfo.Size.Value;
                var downloadedBytes = 0L;
                
                using var downloadStream = await DownloadFileStreamAsync(objectName, cancellationToken);
                int bytesRead;
                while ((bytesRead = await downloadStream.ReadAsync(buffer, 0, buffer.Length, cancellationToken)) > 0)
                {
                    await fileStream.WriteAsync(buffer, 0, bytesRead, cancellationToken);
                    downloadedBytes += bytesRead;
                    var percentage = (double)downloadedBytes / totalBytes * 100;
                    progress.Report(percentage);
                }
            }
            else
            {
                // Direct download without progress
                await _storageClient.DownloadObjectAsync(_bucketName, objectName, fileStream, cancellationToken: cancellationToken);
            }

            _logger.LogInformation("Successfully downloaded: {LocalPath}", localPath);
            return localPath;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to download object: {ObjectName}", objectName);
            throw;
        }
    }

    public async Task<string> UploadFileAsync(string localPath, string objectName, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Uploading file: {LocalPath} as {ObjectName}", localPath, objectName);

            using var fileStream = File.OpenRead(localPath);
            var uploadOptions = new UploadObjectOptions
            {
                ChunkSize = UploadObjectOptions.MinimumChunkSize * 2 // 2MB chunks
            };

            var storageObject = await _storageClient.UploadObjectAsync(
                _bucketName,
                objectName,
                null, // Auto-detect content type
                fileStream,
                uploadOptions,
                cancellationToken);

            _logger.LogInformation("Successfully uploaded: {ObjectName}", objectName);
            return storageObject.MediaLink;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to upload file: {LocalPath}", localPath);
            throw;
        }
    }

    public async Task<bool> FileExistsAsync(string objectName, CancellationToken cancellationToken = default)
    {
        try
        {
            var storageObject = await _storageClient.GetObjectAsync(_bucketName, objectName, null, cancellationToken);
            return storageObject != null;
        }
        catch (Google.GoogleApiException ex) when (ex.Error?.Code == 404)
        {
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking if object exists: {ObjectName}", objectName);
            throw;
        }
    }

    public async Task<IEnumerable<string>> ListFilesAsync(string prefix = "", CancellationToken cancellationToken = default)
    {
        try
        {
            var objects = new List<string>();
            
            await foreach (var storageObject in _storageClient.ListObjectsAsync(_bucketName, prefix))
            {
                if (cancellationToken.IsCancellationRequested)
                    break;
                    
                objects.Add(storageObject.Name);
            }

            _logger.LogInformation("Listed {Count} objects with prefix: {Prefix}", objects.Count, prefix);
            return objects;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to list objects with prefix: {Prefix}", prefix);
            throw;
        }
    }

    public async Task DeleteFileAsync(string objectName, CancellationToken cancellationToken = default)
    {
        try
        {
            await _storageClient.DeleteObjectAsync(_bucketName, objectName, cancellationToken: cancellationToken);
            _logger.LogInformation("Deleted object: {ObjectName}", objectName);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to delete object: {ObjectName}", objectName);
            throw;
        }
    }

    public async Task<string> GenerateSignedUrlAsync(string objectName, TimeSpan expiration)
    {
        try
        {
            if (_credential == null)
            {
                throw new InvalidOperationException("Credential is required to generate signed URLs");
            }

            var urlSigner = UrlSigner.FromCredential(_credential);
            
            var url = await urlSigner.SignAsync(
                _bucketName,
                objectName,
                expiration,
                HttpMethod.Get);

            _logger.LogInformation("Generated signed URL for object: {ObjectName}", objectName);
            return url;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to generate signed URL for object: {ObjectName}", objectName);
            throw;
        }
    }

    public async Task<StorageFileInfo> GetFileInfoAsync(string objectName, CancellationToken cancellationToken = default)
    {
        try
        {
            var storageObject = await _storageClient.GetObjectAsync(_bucketName, objectName, null, cancellationToken);
            
            return new StorageFileInfo
            {
                Name = storageObject.Name,
                Size = storageObject.Size,
                ContentType = storageObject.ContentType,
                Md5Hash = storageObject.Md5Hash,
                Created = storageObject.TimeCreatedDateTimeOffset,
                Updated = storageObject.UpdatedDateTimeOffset,
                Metadata = storageObject.Metadata?.ToDictionary(kvp => kvp.Key, kvp => kvp.Value)
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get info for object: {ObjectName}", objectName);
            throw;
        }
    }

    public async Task<Dictionary<string, string>> GetObjectMetadataAsync(string objectName, CancellationToken cancellationToken = default)
    {
        try
        {
            var storageObject = await _storageClient.GetObjectAsync(_bucketName, objectName, null, cancellationToken);
            return storageObject.Metadata?.ToDictionary(kvp => kvp.Key, kvp => kvp.Value) ?? new Dictionary<string, string>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get metadata for object: {ObjectName}", objectName);
            return new Dictionary<string, string>();
        }
    }

    public async Task UpdateObjectMetadataAsync(string objectName, Dictionary<string, string> metadata, CancellationToken cancellationToken = default)
    {
        try
        {
            var storageObject = await _storageClient.GetObjectAsync(_bucketName, objectName, null, cancellationToken);
            storageObject.Metadata = metadata;
            await _storageClient.UpdateObjectAsync(storageObject, null, cancellationToken);
            _logger.LogInformation("Updated metadata for object: {ObjectName}", objectName);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update metadata for object: {ObjectName}", objectName);
            throw;
        }
    }

    public async Task<string> UploadFileWithMetadataAsync(string localPath, string objectName, Dictionary<string, string> metadata, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Uploading file with metadata: {LocalPath} to {ObjectName}", localPath, objectName);

            using var fileStream = File.OpenRead(localPath);
            var uploadObject = new Object
            {
                Bucket = _bucketName,
                Name = objectName,
                Metadata = metadata
            };

            var result = await _storageClient.UploadObjectAsync(uploadObject, fileStream, cancellationToken: cancellationToken);
            
            _logger.LogInformation("Successfully uploaded file with metadata: {ObjectName}", objectName);
            return result.MediaLink;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to upload file with metadata: {LocalPath}", localPath);
            throw;
        }
    }
    
    public async Task<string> UploadFileWithProgressAsync(string localPath, string objectName, Dictionary<string, string>? metadata, IProgress<UploadProgress>? progress, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Uploading file with progress tracking: {LocalPath} to {ObjectName}", localPath, objectName);
            
            var fileInfo = new FileInfo(localPath);
            var totalBytes = fileInfo.Length;
            var stopwatch = Stopwatch.StartNew();
            var lastReportTime = DateTime.Now;
            var lastBytesUploaded = 0L;
            
            using var fileStream = File.OpenRead(localPath);
            
            // Create custom upload options with progress tracking
            var uploadOptions = new UploadObjectOptions
            {
                ChunkSize = UploadObjectOptions.MinimumChunkSize * 2 // 2MB chunks
            };
            
            // Track bytes uploaded
            var bytesUploaded = 0L;
            var bufferSize = uploadOptions.ChunkSize ?? (UploadObjectOptions.MinimumChunkSize * 2);
            
            // Create a wrapper stream to track progress
            using var progressStream = new ProgressStream(fileStream, totalBytes, (uploaded) =>
            {
                bytesUploaded = uploaded;
                var now = DateTime.Now;
                var timeSinceLastReport = now - lastReportTime;
                
                // Report progress at most once per 100ms to avoid UI flooding
                if (timeSinceLastReport.TotalMilliseconds >= 100 || uploaded == totalBytes)
                {
                    var timeElapsed = stopwatch.Elapsed;
                    var bytesPerSecond = timeElapsed.TotalSeconds > 0 
                        ? (uploaded - lastBytesUploaded) / timeSinceLastReport.TotalSeconds 
                        : 0;
                    
                    var remainingBytes = totalBytes - uploaded;
                    var timeRemaining = bytesPerSecond > 0 
                        ? TimeSpan.FromSeconds(remainingBytes / bytesPerSecond)
                        : TimeSpan.Zero;
                    
                    var uploadProgress = new UploadProgress
                    {
                        BytesUploaded = uploaded,
                        TotalBytes = totalBytes,
                        BytesPerSecond = bytesPerSecond,
                        TimeElapsed = timeElapsed,
                        TimeRemaining = timeRemaining,
                        Status = uploaded == totalBytes ? "Complete" : "Uploading"
                    };
                    
                    progress?.Report(uploadProgress);
                    
                    lastReportTime = now;
                    lastBytesUploaded = uploaded;
                }
            });
            
            var uploadObject = new Object
            {
                Bucket = _bucketName,
                Name = objectName,
                Metadata = metadata
            };
            
            var result = await _storageClient.UploadObjectAsync(
                uploadObject, 
                progressStream, 
                uploadOptions,
                cancellationToken);
            
            stopwatch.Stop();
            
            // Report final progress
            progress?.Report(new UploadProgress
            {
                BytesUploaded = totalBytes,
                TotalBytes = totalBytes,
                BytesPerSecond = 0,
                TimeElapsed = stopwatch.Elapsed,
                TimeRemaining = TimeSpan.Zero,
                Status = "Complete"
            });
            
            _logger.LogInformation("Successfully uploaded file with progress tracking: {ObjectName} in {Time:F2}s", 
                objectName, stopwatch.Elapsed.TotalSeconds);
            
            return result.MediaLink;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to upload file with progress: {LocalPath}", localPath);
            throw;
        }
    }
}

public class StorageFileInfo
{
    public string Name { get; set; } = string.Empty;
    public ulong? Size { get; set; }
    public string? ContentType { get; set; }
    public string? Md5Hash { get; set; }
    public DateTimeOffset? Created { get; set; }
    public DateTimeOffset? Updated { get; set; }
    public Dictionary<string, string>? Metadata { get; set; }
}

public class UploadProgress
{
    public long BytesUploaded { get; set; }
    public long TotalBytes { get; set; }
    public double PercentComplete => TotalBytes > 0 ? (double)BytesUploaded / TotalBytes * 100 : 0;
    public double BytesPerSecond { get; set; }
    public TimeSpan TimeRemaining { get; set; }
    public TimeSpan TimeElapsed { get; set; }
    public string Status { get; set; } = string.Empty;
}