using System.Diagnostics;
using System.Management;
using Microsoft.Extensions.Logging;
using Microsoft.Win32;
using STierDownloader.Core.Models;

namespace STierDownloader.Core.Services;

public interface IEnhancedInstallerService : IInstallerService
{
    Task<InstalledSoftwareInfo?> GetDetailedSoftwareInfoAsync(Software software);
    Task<List<InstalledSoftwareInfo>> GetAllInstalledSoftwareAsync();
    Task<bool> RepairSoftwareAsync(Software software, CancellationToken cancellationToken = default);
    Task<string?> ExtractMsiProductCodeAsync(string msiPath);
    Task<Dictionary<string, string>> GetMsiPropertiesAsync(string msiPath);
}

public class EnhancedInstallerService : IEnhancedInstallerService
{
    private readonly ILogger<EnhancedInstallerService> _logger;
    private readonly IZipExtractionService? _zipService;
    private readonly Dictionary<string, InstalledSoftwareInfo> _installedSoftwareCache = new();
    private DateTime _lastCacheUpdate = DateTime.MinValue;
    private readonly TimeSpan _cacheExpiration = TimeSpan.FromMinutes(5);

    public EnhancedInstallerService(ILogger<EnhancedInstallerService> logger, IZipExtractionService? zipService = null)
    {
        _logger = logger;
        _zipService = zipService;
    }

    public async Task<bool> InstallSoftwareAsync(Software software, CancellationToken cancellationToken = default)
    {
        // Handle zip packages
        if (software.IsZipPackage && _zipService != null)
        {
            return await InstallFromZipPackageAsync(software, cancellationToken);
        }
        
        if (!File.Exists(software.InstallerPath))
        {
            _logger.LogError("Installer not found at {Path}", software.InstallerPath);
            return false;
        }

        try
        {
            _logger.LogInformation("Installing {Software} version {Version}", software.Name, software.Version);

            // Extract MSI product code if it's an MSI
            if (Path.GetExtension(software.InstallerPath).Equals(".msi", StringComparison.OrdinalIgnoreCase))
            {
                var productCode = await ExtractMsiProductCodeAsync(software.InstallerPath);
                if (!string.IsNullOrEmpty(productCode))
                {
                    _logger.LogInformation("MSI Product Code: {ProductCode}", productCode);
                    
                    // Store product code for future uninstall and detection
                    software.MsiProductCode = productCode;
                    
                    if (string.IsNullOrEmpty(software.UninstallCommand))
                    {
                        software.UninstallCommand = $"msiexec.exe /x {productCode} /quiet /norestart";
                    }
                }
            }

            var processInfo = new ProcessStartInfo
            {
                FileName = software.InstallerPath,
                Arguments = software.InstallArguments ?? string.Empty,
                UseShellExecute = false,
                RedirectStandardOutput = true,
                RedirectStandardError = true,
                CreateNoWindow = true
            };

            if (software.RequiresElevation)
            {
                processInfo.Verb = "runas";
                processInfo.UseShellExecute = true;
                processInfo.RedirectStandardOutput = false;
                processInfo.RedirectStandardError = false;
            }

            using var process = Process.Start(processInfo);
            if (process == null)
            {
                _logger.LogError("Failed to start installer process");
                return false;
            }

            await process.WaitForExitAsync(cancellationToken);

            if (process.ExitCode == 0 || process.ExitCode == 3010) // 3010 = restart required
            {
                _logger.LogInformation("Successfully installed {Software}", software.Name);
                
                // Clear cache to force refresh
                _installedSoftwareCache.Clear();
                
                if (process.ExitCode == 3010)
                {
                    _logger.LogWarning("Installation successful but restart required");
                }
                
                return true;
            }
            else
            {
                _logger.LogError("Installation failed with exit code {ExitCode}", process.ExitCode);
                return false;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Installation failed for {Software}", software.Name);
            return false;
        }
    }

    public async Task<bool> UninstallSoftwareAsync(Software software, CancellationToken cancellationToken = default)
    {
        // Try to get detailed info for better uninstall
        var installedInfo = await GetDetailedSoftwareInfoAsync(software);
        
        string uninstallCommand;
        if (installedInfo != null)
        {
            uninstallCommand = installedInfo.GetEffectiveUninstallCommand();
            _logger.LogInformation("Using detected uninstall command: {Command}", uninstallCommand);
        }
        else if (!string.IsNullOrEmpty(software.UninstallCommand))
        {
            uninstallCommand = software.UninstallCommand;
            _logger.LogInformation("Using configured uninstall command: {Command}", uninstallCommand);
        }
        else
        {
            _logger.LogWarning("No uninstall command available for {Software}", software.Name);
            return false;
        }

        try
        {
            _logger.LogInformation("Uninstalling {Software}", software.Name);

            // Parse command and arguments
            string fileName;
            string arguments = "";
            
            if (uninstallCommand.StartsWith("msiexec", StringComparison.OrdinalIgnoreCase))
            {
                fileName = "msiexec.exe";
                arguments = uninstallCommand.Substring(uninstallCommand.IndexOf(' ') + 1);
            }
            else if (uninstallCommand.Contains(' '))
            {
                var parts = uninstallCommand.Split(' ', 2);
                fileName = parts[0];
                arguments = parts.Length > 1 ? parts[1] : "";
            }
            else
            {
                fileName = uninstallCommand;
            }

            var processInfo = new ProcessStartInfo
            {
                FileName = fileName,
                Arguments = arguments,
                UseShellExecute = false,
                RedirectStandardOutput = true,
                RedirectStandardError = true,
                CreateNoWindow = true
            };

            if (software.RequiresElevation)
            {
                processInfo.Verb = "runas";
                processInfo.UseShellExecute = true;
                processInfo.RedirectStandardOutput = false;
                processInfo.RedirectStandardError = false;
            }

            using var process = Process.Start(processInfo);
            if (process == null)
            {
                _logger.LogError("Failed to start uninstaller process");
                return false;
            }

            await process.WaitForExitAsync(cancellationToken);

            if (process.ExitCode == 0 || process.ExitCode == 3010)
            {
                _logger.LogInformation("Successfully uninstalled {Software}", software.Name);
                
                // Clear cache
                _installedSoftwareCache.Clear();
                
                if (process.ExitCode == 3010)
                {
                    _logger.LogWarning("Uninstall successful but restart required");
                }
                
                return true;
            }
            else
            {
                _logger.LogError("Uninstallation failed with exit code {ExitCode}", process.ExitCode);
                return false;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Uninstallation failed for {Software}", software.Name);
            return false;
        }
    }

    public async Task<bool> IsSoftwareInstalledAsync(Software software)
    {
        var installedInfo = await GetDetailedSoftwareInfoAsync(software);
        return installedInfo != null;
    }

    public async Task<string?> GetInstalledVersionAsync(Software software)
    {
        var installedInfo = await GetDetailedSoftwareInfoAsync(software);
        return installedInfo?.DisplayVersion;
    }

    public async Task<InstalledSoftwareInfo?> GetDetailedSoftwareInfoAsync(Software software)
    {
        // Refresh cache if expired
        if (DateTime.Now - _lastCacheUpdate > _cacheExpiration)
        {
            await RefreshInstalledSoftwareCacheAsync();
        }

        // Search in cache
        foreach (var kvp in _installedSoftwareCache)
        {
            if (kvp.Value.MatchesSoftware(software))
            {
                return kvp.Value;
            }
        }

        return null;
    }

    public async Task<List<InstalledSoftwareInfo>> GetAllInstalledSoftwareAsync()
    {
        if (DateTime.Now - _lastCacheUpdate > _cacheExpiration)
        {
            await RefreshInstalledSoftwareCacheAsync();
        }

        return _installedSoftwareCache.Values.ToList();
    }

    private async Task RefreshInstalledSoftwareCacheAsync()
    {
        await Task.Run(() =>
        {
            _installedSoftwareCache.Clear();
            
            var registryPaths = new[]
            {
                (Registry.LocalMachine, @"SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall"),
                (Registry.LocalMachine, @"SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Uninstall"),
                (Registry.CurrentUser, @"SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall")
            };

            foreach (var (hive, path) in registryPaths)
            {
                try
                {
                    using var key = hive.OpenSubKey(path);
                    if (key == null) continue;

                    foreach (var subKeyName in key.GetSubKeyNames())
                    {
                        try
                        {
                            using var subKey = key.OpenSubKey(subKeyName);
                            if (subKey == null) continue;

                            var displayName = subKey.GetValue("DisplayName") as string;
                            if (string.IsNullOrEmpty(displayName)) continue;

                            // Skip Windows updates and system components
                            var systemComponent = subKey.GetValue("SystemComponent") as int?;
                            if (systemComponent == 1) continue;

                            var info = new InstalledSoftwareInfo
                            {
                                DisplayName = displayName,
                                DisplayVersion = subKey.GetValue("DisplayVersion") as string,
                                Publisher = subKey.GetValue("Publisher") as string,
                                InstallDate = subKey.GetValue("InstallDate") as string,
                                InstallLocation = subKey.GetValue("InstallLocation") as string,
                                UninstallString = subKey.GetValue("UninstallString") as string,
                                QuietUninstallString = subKey.GetValue("QuietUninstallString") as string,
                                ModifyPath = subKey.GetValue("ModifyPath") as string,
                                EstimatedSize = subKey.GetValue("EstimatedSize") as int? as long?,
                                Icon = subKey.GetValue("DisplayIcon") as string,
                                HelpLink = subKey.GetValue("HelpLink") as string,
                                URLInfoAbout = subKey.GetValue("URLInfoAbout") as string,
                                Comments = subKey.GetValue("Comments") as string,
                                VersionMajor = subKey.GetValue("VersionMajor") as int?,
                                VersionMinor = subKey.GetValue("VersionMinor") as int?,
                                NoModify = (subKey.GetValue("NoModify") as int?) == 1,
                                NoRemove = (subKey.GetValue("NoRemove") as int?) == 1,
                                NoRepair = (subKey.GetValue("NoRepair") as int?) == 1,
                                RegistryPath = path,
                                RegistryKey = subKeyName
                            };

                            // Check if it's an MSI installation
                            if (subKeyName.StartsWith("{") && subKeyName.EndsWith("}"))
                            {
                                info.ProductCode = subKeyName;
                                info.IsWindowsInstaller = true;
                            }
                            else if (!string.IsNullOrEmpty(info.UninstallString) && 
                                     info.UninstallString.Contains("msiexec", StringComparison.OrdinalIgnoreCase))
                            {
                                info.IsWindowsInstaller = true;
                                
                                // Extract product code from uninstall string
                                var match = System.Text.RegularExpressions.Regex.Match(
                                    info.UninstallString, 
                                    @"\{[A-F0-9]{8}-[A-F0-9]{4}-[A-F0-9]{4}-[A-F0-9]{4}-[A-F0-9]{12}\}",
                                    System.Text.RegularExpressions.RegexOptions.IgnoreCase);
                                    
                                if (match.Success)
                                {
                                    info.ProductCode = match.Value;
                                }
                            }

                            // Parse install date
                            if (!string.IsNullOrEmpty(info.InstallDate) && info.InstallDate.Length == 8)
                            {
                                if (int.TryParse(info.InstallDate.Substring(0, 4), out var year) &&
                                    int.TryParse(info.InstallDate.Substring(4, 2), out var month) &&
                                    int.TryParse(info.InstallDate.Substring(6, 2), out var day))
                                {
                                    info.InstallDateParsed = new DateTime(year, month, day);
                                }
                            }

                            // Parse version
                            if (!string.IsNullOrEmpty(info.DisplayVersion))
                            {
                                if (Version.TryParse(info.DisplayVersion, out var version))
                                {
                                    info.ParsedVersion = version;
                                }
                            }

                            // Use product code as key if available, otherwise use display name
                            var cacheKey = !string.IsNullOrEmpty(info.ProductCode) 
                                ? info.ProductCode 
                                : displayName;
                                
                            _installedSoftwareCache[cacheKey] = info;
                        }
                        catch (Exception ex)
                        {
                            _logger.LogDebug(ex, "Error reading registry key {Key}", subKeyName);
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Error accessing registry path {Path}", path);
                }
            }

            _lastCacheUpdate = DateTime.Now;
            _logger.LogInformation("Refreshed installed software cache. Found {Count} installed programs", _installedSoftwareCache.Count);
        });
    }

    public async Task<bool> RepairSoftwareAsync(Software software, CancellationToken cancellationToken = default)
    {
        var installedInfo = await GetDetailedSoftwareInfoAsync(software);
        
        if (installedInfo == null)
        {
            _logger.LogWarning("Software {Name} not found in installed programs", software.Name);
            return false;
        }

        if (!installedInfo.CanRepair)
        {
            _logger.LogWarning("Software {Name} does not support repair", software.Name);
            return false;
        }

        try
        {
            _logger.LogInformation("Repairing {Software}", software.Name);

            var processInfo = new ProcessStartInfo
            {
                FileName = "msiexec.exe",
                Arguments = $"/fa {installedInfo.ProductCode} /quiet",
                UseShellExecute = false,
                CreateNoWindow = true
            };

            if (software.RequiresElevation)
            {
                processInfo.Verb = "runas";
                processInfo.UseShellExecute = true;
            }

            using var process = Process.Start(processInfo);
            if (process == null)
            {
                _logger.LogError("Failed to start repair process");
                return false;
            }

            await process.WaitForExitAsync(cancellationToken);

            if (process.ExitCode == 0 || process.ExitCode == 3010)
            {
                _logger.LogInformation("Successfully repaired {Software}", software.Name);
                return true;
            }
            else
            {
                _logger.LogError("Repair failed with exit code {ExitCode}", process.ExitCode);
                return false;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Repair failed for {Software}", software.Name);
            return false;
        }
    }

    public async Task<string?> ExtractMsiProductCodeAsync(string msiPath)
    {
        return await Task.Run(() =>
        {
            try
            {
                // Use Windows Installer API via COM
                Type installerType = Type.GetTypeFromProgID("WindowsInstaller.Installer");
                if (installerType == null) return null;

                dynamic installer = Activator.CreateInstance(installerType);
                dynamic database = installer.OpenDatabase(msiPath, 0); // 0 = read-only
                
                try
                {
                    // Query the Property table for ProductCode
                    dynamic view = database.OpenView("SELECT Value FROM Property WHERE Property = 'ProductCode'");
                    view.Execute();
                    dynamic record = view.Fetch();
                    
                    if (record != null)
                    {
                        string productCode = record.StringData[1];
                        _logger.LogInformation("Extracted Product Code from MSI: {ProductCode}", productCode);
                        return productCode;
                    }
                }
                finally
                {
                    System.Runtime.InteropServices.Marshal.ReleaseComObject(database);
                    System.Runtime.InteropServices.Marshal.ReleaseComObject(installer);
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to extract product code from MSI: {Path}", msiPath);
            }

            return null;
        });
    }

    public async Task<Dictionary<string, string>> GetMsiPropertiesAsync(string msiPath)
    {
        return await Task.Run(() =>
        {
            var properties = new Dictionary<string, string>();

            try
            {
                Type installerType = Type.GetTypeFromProgID("WindowsInstaller.Installer");
                if (installerType == null) return properties;

                dynamic installer = Activator.CreateInstance(installerType);
                dynamic database = installer.OpenDatabase(msiPath, 0); // 0 = read-only
                
                try
                {
                    // Query all properties
                    dynamic view = database.OpenView("SELECT Property, Value FROM Property");
                    view.Execute();
                    
                    dynamic record;
                    while ((record = view.Fetch()) != null)
                    {
                        string property = record.StringData[1];
                        string value = record.StringData[2];
                        properties[property] = value;
                    }
                    
                    _logger.LogInformation("Extracted {Count} properties from MSI", properties.Count);
                }
                finally
                {
                    System.Runtime.InteropServices.Marshal.ReleaseComObject(database);
                    System.Runtime.InteropServices.Marshal.ReleaseComObject(installer);
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to extract properties from MSI: {Path}", msiPath);
            }

            return properties;
        });
    }
    
    private async Task<bool> InstallFromZipPackageAsync(Software software, CancellationToken cancellationToken = default)
    {
        if (!File.Exists(software.InstallerPath))
        {
            _logger.LogError("Zip package not found at {Path}", software.InstallerPath);
            return false;
        }
        
        if (_zipService == null)
        {
            _logger.LogError("Zip extraction service not available");
            return false;
        }
        
        try
        {
            _logger.LogInformation("Installing {Software} from zip package", software.Name);
            
            // Extract the zip file
            var extractProgress = new Progress<double>(p => 
                _logger.LogDebug("Extraction progress: {Progress}%", p));
            
            var extractedPath = await _zipService.ExtractZipAsync(software.InstallerPath, progress: extractProgress);
            software.ExtractedPath = extractedPath;
            
            // Find the installer in extracted files
            var installerPath = await _zipService.FindInstallerInExtractedFiles(extractedPath, software);
            if (string.IsNullOrEmpty(installerPath))
            {
                _logger.LogError("No installer found in extracted files");
                
                // Cleanup extraction
                if (software.CleanupAfterInstall)
                {
                    await _zipService.CleanupExtractedFilesAsync(extractedPath);
                }
                
                return false;
            }
            
            // Determine install arguments based on Niagara variant
            var installArgs = GetNiagaraInstallArguments(software);
            
            // Run the installer
            var processInfo = new ProcessStartInfo
            {
                FileName = installerPath,
                Arguments = installArgs,
                UseShellExecute = false,
                RedirectStandardOutput = true,
                RedirectStandardError = true,
                CreateNoWindow = true,
                WorkingDirectory = extractedPath
            };
            
            if (software.RequiresElevation)
            {
                processInfo.Verb = "runas";
                processInfo.UseShellExecute = true;
                processInfo.RedirectStandardOutput = false;
                processInfo.RedirectStandardError = false;
            }
            
            _logger.LogInformation("Running installer: {Installer} {Args}", installerPath, installArgs);
            
            using var process = Process.Start(processInfo);
            if (process == null)
            {
                _logger.LogError("Failed to start installer process");
                return false;
            }
            
            await process.WaitForExitAsync(cancellationToken);
            
            var success = process.ExitCode == 0 || process.ExitCode == 3010;
            
            if (success)
            {
                _logger.LogInformation("Successfully installed {Software}", software.Name);
                
                // Update expected install path based on variant
                UpdateNiagaraInstallPath(software);
                
                if (process.ExitCode == 3010)
                {
                    _logger.LogWarning("Installation successful but restart required");
                }
            }
            else
            {
                _logger.LogError("Installation failed with exit code {ExitCode}", process.ExitCode);
            }
            
            // Cleanup extracted files if requested
            if (software.CleanupAfterInstall)
            {
                _logger.LogInformation("Cleaning up extracted files");
                await _zipService.CleanupExtractedFilesAsync(extractedPath);
                software.ExtractedPath = null;
            }
            
            // Clear cache to force refresh
            _installedSoftwareCache.Clear();
            
            return success;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to install {Software} from zip package", software.Name);
            
            // Attempt cleanup on error
            if (!string.IsNullOrEmpty(software.ExtractedPath) && software.CleanupAfterInstall)
            {
                try
                {
                    await _zipService.CleanupExtractedFilesAsync(software.ExtractedPath);
                }
                catch { }
            }
            
            return false;
        }
    }
    
    private string GetNiagaraInstallArguments(Software software)
    {
        // Common silent install arguments for Niagara
        var baseArgs = "/S /v/qn";
        
        // Add variant-specific arguments
        if (software.NiagaraVariant.HasValue)
        {
            switch (software.NiagaraVariant.Value)
            {
                case NiagaraVariant.Vykon:
                    // Vykon-specific install directory
                    baseArgs += " INSTALLDIR=\"C:\\Vykon\"";
                    break;
                    
                case NiagaraVariant.Honeywell:
                    // Honeywell WEBs-N4 install directory
                    baseArgs += " INSTALLDIR=\"C:\\Honeywell\\WEBs-N4\"";
                    break;
                    
                case NiagaraVariant.Distech:
                    // Distech EC-Net install directory
                    baseArgs += " INSTALLDIR=\"C:\\Distech\\EC-Net\"";
                    break;
                    
                case NiagaraVariant.Lynxspring:
                    // Lynxspring JENEsys install directory
                    baseArgs += " INSTALLDIR=\"C:\\Lynxspring\\JENEsys\"";
                    break;
                    
                default:
                    // Standard Niagara directory
                    baseArgs += " INSTALLDIR=\"C:\\Niagara\\Niagara-4.4\"";
                    break;
            }
        }
        
        // Override with custom arguments if provided
        if (!string.IsNullOrEmpty(software.InstallArguments))
        {
            return software.InstallArguments;
        }
        
        return baseArgs;
    }
    
    private void UpdateNiagaraInstallPath(Software software)
    {
        if (software.Type != SoftwareType.TridiumNiagara)
            return;
        
        // Set expected install path and executable based on variant
        switch (software.NiagaraVariant)
        {
            case NiagaraVariant.Vykon:
                software.ExpectedInstallPath = @"C:\Vykon";
                software.MainExecutable = "wb.exe";
                break;
                
            case NiagaraVariant.Honeywell:
                software.ExpectedInstallPath = @"C:\Honeywell\WEBs-N4";
                software.MainExecutable = "wb.exe";
                break;
                
            case NiagaraVariant.Distech:
                software.ExpectedInstallPath = @"C:\Distech\EC-Net";
                software.MainExecutable = "wb.exe";
                break;
                
            case NiagaraVariant.Lynxspring:
                software.ExpectedInstallPath = @"C:\Lynxspring\JENEsys";
                software.MainExecutable = "wb.exe";
                break;
                
            default:
                software.ExpectedInstallPath = @"C:\Niagara\Niagara-4.4";
                software.MainExecutable = "wb.exe";
                break;
        }
        
        _logger.LogInformation("Set Niagara install path to {Path} for variant {Variant}", 
            software.ExpectedInstallPath, software.NiagaraVariant);
    }
}