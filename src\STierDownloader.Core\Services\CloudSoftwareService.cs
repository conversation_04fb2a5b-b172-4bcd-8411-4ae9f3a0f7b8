using Microsoft.Extensions.Logging;
using STierDownloader.Core.Models;
using System.Text.Json;

namespace STierDownloader.Core.Services;

public interface ICloudSoftwareService
{
    Task<List<Software>> GetAvailableSoftwareAsync(CancellationToken cancellationToken = default);
    Task<bool> UpdateSoftwareMetadataAsync(string objectName, Software software, CancellationToken cancellationToken = default);
    Task<Software?> GetSoftwareFromObjectAsync(string objectName, CancellationToken cancellationToken = default);
}

public class CloudSoftwareService : ICloudSoftwareService
{
    private readonly ICloudStorageService? _cloudStorageService;
    private readonly ILogger<CloudSoftwareService> _logger;
    private const string SoftwarePrefix = "software/";
    
    public CloudSoftwareService(
        ICloudStorageService? cloudStorageService,
        ILogger<CloudSoftwareService> logger)
    {
        _cloudStorageService = cloudStorageService;
        _logger = logger;
    }

    public async Task<List<Software>> GetAvailableSoftwareAsync(CancellationToken cancellationToken = default)
    {
        var softwareList = new List<Software>();
        
        if (_cloudStorageService == null)
        {
            _logger.LogWarning("Cloud storage service is not configured");
            return softwareList;
        }

        try
        {
            // List all files in the software folder
            var files = await _cloudStorageService.ListFilesAsync(SoftwarePrefix, cancellationToken);
            
            foreach (var file in files)
            {
                try
                {
                    var software = await GetSoftwareFromObjectAsync(file, cancellationToken);
                    if (software != null)
                    {
                        softwareList.Add(software);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to load software metadata for: {File}", file);
                }
            }
            
            _logger.LogInformation("Loaded {Count} software items from cloud storage", softwareList.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to list software from cloud storage");
        }
        
        return softwareList;
    }

    public async Task<Software?> GetSoftwareFromObjectAsync(string objectName, CancellationToken cancellationToken = default)
    {
        if (_cloudStorageService == null)
            return null;

        try
        {
            // Get object metadata
            var metadata = await _cloudStorageService.GetObjectMetadataAsync(objectName, cancellationToken);
            
            if (metadata == null || !metadata.ContainsKey("software-name"))
            {
                // If no metadata, try to infer from filename
                return InferSoftwareFromFilename(objectName);
            }
            
            // Build software object from metadata
            // Get and sanitize the software name
            var rawName = metadata.GetValueOrDefault("software-name", Path.GetFileNameWithoutExtension(objectName));
            var sanitizedName = SanitizeSoftwareName(rawName, objectName);
            
            var software = new Software
            {
                Name = sanitizedName,
                Version = metadata.GetValueOrDefault("software-version", "Unknown"),
                Type = Enum.TryParse<SoftwareType>(metadata.GetValueOrDefault("software-type", "Utility"), out var type) 
                    ? type : SoftwareType.Utility,
                StorageLocation = StorageLocation.GoogleCloudStorage,
                CloudObjectName = objectName,
                InstallArguments = metadata.GetValueOrDefault("install-arguments", GetDefaultInstallArguments(objectName)),
                UninstallCommand = metadata.GetValueOrDefault("uninstall-command", ""),
                ChecksumSha256 = metadata.GetValueOrDefault("checksum-sha256", ""),
                RequiresElevation = bool.TryParse(metadata.GetValueOrDefault("requires-elevation", "true"), out var elevated) 
                    ? elevated : true,
                MsiProductCode = metadata.GetValueOrDefault("msi-product-code", ""),
                ExpectedInstallPath = metadata.GetValueOrDefault("expected-install-path", ""),
                MainExecutable = metadata.GetValueOrDefault("main-executable", ""),
                IsZipPackage = Path.GetExtension(objectName).Equals(".zip", StringComparison.OrdinalIgnoreCase),
                CleanupAfterInstall = bool.TryParse(metadata.GetValueOrDefault("cleanup-after-install", "true"), out var cleanup) 
                    ? cleanup : true
            };
            
            // Parse Niagara variant if present
            if (metadata.ContainsKey("niagara-variant") && 
                Enum.TryParse<NiagaraVariant>(metadata["niagara-variant"], out var variant))
            {
                software.NiagaraVariant = variant;
            }
            else if (software.Type == SoftwareType.TridiumNiagara)
            {
                // Try to detect variant from name
                software.NiagaraVariant = DetectNiagaraVariant(software.Name);
            }
            
            // Additional metadata can be stored but not directly on Software object
            // These would be available through the metadata dictionary if needed
            
            return software;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get software metadata for: {Object}", objectName);
            return InferSoftwareFromFilename(objectName);
        }
    }

    public async Task<bool> UpdateSoftwareMetadataAsync(
        string objectName, 
        Software software, 
        CancellationToken cancellationToken = default)
    {
        if (_cloudStorageService == null)
            return false;

        try
        {
            var metadata = new Dictionary<string, string>
            {
                ["software-name"] = software.Name,
                ["software-version"] = software.Version,
                ["software-type"] = software.Type.ToString(),
                ["install-arguments"] = software.InstallArguments ?? "",
                ["requires-elevation"] = software.RequiresElevation.ToString()
            };
            
            // Add optional metadata
            if (!string.IsNullOrEmpty(software.UninstallCommand))
                metadata["uninstall-command"] = software.UninstallCommand;
            
            if (!string.IsNullOrEmpty(software.ChecksumSha256))
                metadata["checksum-sha256"] = software.ChecksumSha256;
            
            if (!string.IsNullOrEmpty(software.MsiProductCode))
                metadata["msi-product-code"] = software.MsiProductCode;
            
            if (!string.IsNullOrEmpty(software.ExpectedInstallPath))
                metadata["expected-install-path"] = software.ExpectedInstallPath;
            
            if (!string.IsNullOrEmpty(software.MainExecutable))
                metadata["main-executable"] = software.MainExecutable;
            
            if (software.NiagaraVariant.HasValue)
                metadata["niagara-variant"] = software.NiagaraVariant.Value.ToString();
            
            if (software.IsZipPackage)
                metadata["is-zip-package"] = "true";
            
            metadata["cleanup-after-install"] = software.CleanupAfterInstall.ToString();
            
            await _cloudStorageService.UpdateObjectMetadataAsync(objectName, metadata, cancellationToken);
            
            _logger.LogInformation("Updated metadata for: {Name} v{Version}", software.Name, software.Version);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update software metadata");
            return false;
        }
    }

    private Software InferSoftwareFromFilename(string objectName)
    {
        var fileName = Path.GetFileName(objectName);
        var nameWithoutExt = Path.GetFileNameWithoutExtension(fileName);
        
        // Try to parse version from filename
        var version = "Unknown";
        var name = nameWithoutExt;
        
        // Common version patterns
        var versionPattern = @"[-_\.v](\d+(?:\.\d+)*)";
        var match = System.Text.RegularExpressions.Regex.Match(nameWithoutExt, versionPattern);
        if (match.Success)
        {
            version = match.Groups[1].Value;
            name = nameWithoutExt.Substring(0, match.Index).TrimEnd('-', '_', '.');
        }
        
        // Clean up the name
        name = name.Replace("-", " ").Replace("_", " ");
        
        // Determine type from path
        var type = SoftwareType.Utility;
        if (objectName.Contains("/niagara/", StringComparison.OrdinalIgnoreCase))
            type = SoftwareType.TridiumNiagara;
        else if (objectName.Contains("/vpn/", StringComparison.OrdinalIgnoreCase))
            type = SoftwareType.VpnClient;
        else if (objectName.Contains("/hvac-tools/", StringComparison.OrdinalIgnoreCase))
            type = SoftwareType.HvacProgrammingTool;
        else if (objectName.Contains("/drivers/", StringComparison.OrdinalIgnoreCase))
            type = SoftwareType.Driver;
        else if (objectName.Contains("/frameworks/", StringComparison.OrdinalIgnoreCase))
            type = SoftwareType.Framework;
        
        var software = new Software
        {
            Name = name,
            Version = version,
            Type = type,
            StorageLocation = StorageLocation.GoogleCloudStorage,
            CloudObjectName = objectName,
            InstallArguments = GetDefaultInstallArguments(objectName),
            RequiresElevation = true,
            IsZipPackage = Path.GetExtension(objectName).Equals(".zip", StringComparison.OrdinalIgnoreCase),
            CleanupAfterInstall = true
        };
        
        // Detect Niagara variant if it's a Niagara software
        if (type == SoftwareType.TridiumNiagara)
        {
            software.NiagaraVariant = DetectNiagaraVariant(name);
        }
        
        return software;
    }
    
    private string GetDefaultInstallArguments(string filename)
    {
        var extension = Path.GetExtension(filename).ToLowerInvariant();
        return extension switch
        {
            ".msi" => "/quiet /norestart",
            ".exe" => "/S /v/qn",
            ".zip" => "", // Will be handled by zip extraction
            _ => ""
        };
    }
    
    private string SanitizeSoftwareName(string name, string objectName)
    {
        // Check for common metadata corruption patterns
        if (string.IsNullOrWhiteSpace(name) || 
            name.Contains("(vl <") || 
            name.Contains("< CompanyName") ||
            name.Contains("<CompanyName>") ||
            name.StartsWith("<") ||
            name.Contains("${")||  
            name.Contains("{{")||  
            name.Equals("null", StringComparison.OrdinalIgnoreCase) ||
            name.Equals("undefined", StringComparison.OrdinalIgnoreCase))
        {
            _logger.LogWarning("Detected corrupt metadata name: '{Name}' for object: {Object}", name, objectName);
            
            // Try to infer a better name from the file path
            var fileName = Path.GetFileNameWithoutExtension(objectName);
            var pathLower = objectName.ToLowerInvariant();
            
            // Check for known product patterns
            if (pathLower.Contains("vykon"))
            {
                return "Vykon Niagara 4";
            }
            else if (pathLower.Contains("honeywell") || pathLower.Contains("webs"))
            {
                return "Honeywell WEBs-N4";
            }
            else if (pathLower.Contains("distech") || pathLower.Contains("ec-net"))
            {
                return "Distech EC-Net";
            }
            else if (pathLower.Contains("lynxspring") || pathLower.Contains("jenesys"))
            {
                return "Lynxspring JENEsys";
            }
            else if (pathLower.Contains("niagara"))
            {
                return "Tridium Niagara 4";
            }
            else
            {
                // Use cleaned filename as fallback
                return CleanFileName(fileName);
            }
        }
        
        return name;
    }
    
    private string CleanFileName(string fileName)
    {
        // Remove version numbers and clean up filename
        var cleaned = System.Text.RegularExpressions.Regex.Replace(fileName, @"[-_\.](\d+\.)+\d+.*$", "");
        cleaned = cleaned.Replace("-", " ").Replace("_", " ");
        
        // Capitalize words
        var words = cleaned.Split(' ', StringSplitOptions.RemoveEmptyEntries);
        for (int i = 0; i < words.Length; i++)
        {
            if (words[i].Length > 0)
            {
                words[i] = char.ToUpper(words[i][0]) + words[i].Substring(1).ToLower();
            }
        }
        
        return string.Join(" ", words);
    }
    
    private NiagaraVariant DetectNiagaraVariant(string name)
    {
        var lowerName = name.ToLowerInvariant();
        
        if (lowerName.Contains("vykon"))
            return NiagaraVariant.Vykon;
        
        if (lowerName.Contains("honeywell") || lowerName.Contains("webs"))
            return NiagaraVariant.Honeywell;
        
        if (lowerName.Contains("distech") || lowerName.Contains("ec-net"))
            return NiagaraVariant.Distech;
        
        if (lowerName.Contains("lynxspring") || lowerName.Contains("jenesys"))
            return NiagaraVariant.Lynxspring;
        
        if (lowerName.Contains("niagara"))
            return NiagaraVariant.Standard;
        
        return NiagaraVariant.Other;
    }
}