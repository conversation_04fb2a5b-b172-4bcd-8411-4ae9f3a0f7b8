<Window x:Class="STierDownloader.UI.Views.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:converters="clr-namespace:STierDownloader.UI.Converters"
        mc:Ignorable="d"
        Title="S-Tier Downloader - Building Automation Software Manager"
        Height="650" Width="1000"
        WindowStartupLocation="CenterScreen">
    
    <Window.Resources>
        <converters:InverseBooleanConverter x:Key="InverseBooleanConverter"/>
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
    </Window.Resources>
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="#FF1E88E5" Padding="20">
            <StackPanel>
                <TextBlock Text="Building Automation Software Manager" 
                          FontSize="24" FontWeight="Bold" Foreground="White"/>
                <TextBlock Text="Download, Install, and Update HVAC/BAS Software" 
                          FontSize="14" Foreground="#E3F2FD" Margin="0,5,0,0"/>
            </StackPanel>
        </Border>

        <!-- Toolbar -->
        <ToolBar Grid.Row="1" Padding="5">
            <Button Command="{Binding RefreshAllCommand}" Padding="10,5">
                <StackPanel Orientation="Horizontal">
                    <TextBlock Text="🔄" Margin="0,0,5,0"/>
                    <TextBlock Text="Refresh All"/>
                </StackPanel>
            </Button>
            <Separator/>
            <Button Command="{Binding DownloadAllCommand}" Padding="10,5">
                <StackPanel Orientation="Horizontal">
                    <TextBlock Text="⬇" Margin="0,0,5,0"/>
                    <TextBlock Text="Download All"/>
                </StackPanel>
            </Button>
            <Button Command="{Binding InstallAllCommand}" Padding="10,5">
                <StackPanel Orientation="Horizontal">
                    <TextBlock Text="📦" Margin="0,0,5,0"/>
                    <TextBlock Text="Install All"/>
                </StackPanel>
            </Button>
            <Separator/>
            <Button Command="{Binding OpenUploadWindowCommand}" Padding="10,5"
                    Background="#FF9C27B0" Foreground="White">
                <StackPanel Orientation="Horizontal">
                    <TextBlock Text="☁" Margin="0,0,5,0"/>
                    <TextBlock Text="Admin Upload"/>
                </StackPanel>
            </Button>
        </ToolBar>

        <!-- Software List -->
        <DataGrid Grid.Row="2" 
                  ItemsSource="{Binding SoftwareList}"
                  SelectedItem="{Binding SelectedSoftware}"
                  AutoGenerateColumns="False"
                  CanUserAddRows="False"
                  Margin="10"
                  GridLinesVisibility="Horizontal"
                  HeadersVisibility="Column">
            <DataGrid.Columns>
                <DataGridTextColumn Header="Software" Binding="{Binding Name}" Width="250"/>
                <DataGridTextColumn Header="Version" Binding="{Binding Version}" Width="80"/>
                <DataGridTextColumn Header="Type" Binding="{Binding Type}" Width="150"/>
                <DataGridTextColumn Header="Status" Binding="{Binding Status}" Width="150"/>
                
                <DataGridTemplateColumn Header="Progress" Width="150">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <ProgressBar Value="{Binding DownloadProgress}" 
                                       Minimum="0" Maximum="100" 
                                       Height="20"
                                       Visibility="{Binding IsDownloading, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                </DataGridTemplateColumn>
                
                <DataGridTemplateColumn Header="Action" Width="150">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <Grid>
                                <Button Content="{Binding ActionButtonText}" 
                                       Command="{Binding ExecuteActionCommand}"
                                       IsEnabled="{Binding ActionButtonEnabled}"
                                       Margin="2"
                                       Padding="10,6"
                                       FontWeight="SemiBold"
                                       HorizontalAlignment="Center"
                                       MinWidth="100">
                                    <Button.Style>
                                        <Style TargetType="Button">
                                            <Setter Property="Background" Value="#FF2196F3"/>
                                            <Setter Property="Foreground" Value="White"/>
                                            <Style.Triggers>
                                                <!-- Download state - Blue -->
                                                <DataTrigger Binding="{Binding ActionButtonText}" Value="Download">
                                                    <Setter Property="Background" Value="#FF2196F3"/>
                                                </DataTrigger>
                                                <!-- Install state - Green -->
                                                <DataTrigger Binding="{Binding ActionButtonText}" Value="Install">
                                                    <Setter Property="Background" Value="#FF4CAF50"/>
                                                </DataTrigger>
                                                <!-- Launch state - Dark Blue -->
                                                <DataTrigger Binding="{Binding ActionButtonText}" Value="Launch">
                                                    <Setter Property="Background" Value="#FF1976D2"/>
                                                </DataTrigger>
                                                <!-- Update state - Orange -->
                                                <DataTrigger Binding="{Binding ActionButtonText}" Value="Update">
                                                    <Setter Property="Background" Value="#FFFF9800"/>
                                                </DataTrigger>
                                                <!-- Processing states - Gray -->
                                                <DataTrigger Binding="{Binding IsProcessing}" Value="True">
                                                    <Setter Property="Background" Value="#FF757575"/>
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </Button.Style>
                                </Button>
                            </Grid>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                </DataGridTemplateColumn>
                
                <DataGridTemplateColumn Header="Admin" Width="80">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <Button Content="Uninstall" 
                                   Command="{Binding UninstallCommand}"
                                   Margin="2"
                                   Padding="6,3"
                                   FontSize="11"
                                   IsEnabled="{Binding IsInstalled}"
                                   Background="#FFEF5350"
                                   Foreground="White"/>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                </DataGridTemplateColumn>
            </DataGrid.Columns>
        </DataGrid>

        <!-- Status Bar -->
        <StatusBar Grid.Row="3">
            <StatusBarItem>
                <TextBlock Text="{Binding StatusMessage}"/>
            </StatusBarItem>
            <StatusBarItem HorizontalAlignment="Right">
                <ProgressBar Width="100" Height="16" 
                           IsIndeterminate="{Binding IsLoading}"
                           Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}"/>
            </StatusBarItem>
        </StatusBar>
    </Grid>
</Window>