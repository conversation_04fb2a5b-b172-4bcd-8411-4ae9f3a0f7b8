using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using STierDownloader.Core.Models;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace STierDownloader.Core.Services;

public interface IConfigurationSyncService
{
    Task<bool> DownloadConfigurationAsync(CancellationToken cancellationToken = default);
    Task<bool> UploadConfigurationAsync(CancellationToken cancellationToken = default);
    Task<bool> BackupConfigurationAsync(string? backupName = null, CancellationToken cancellationToken = default);
    Task<List<ConfigurationVersion>> GetConfigurationVersionsAsync(CancellationToken cancellationToken = default);
    Task<bool> RestoreConfigurationAsync(string versionId, CancellationToken cancellationToken = default);
    Task<bool> SyncConfigurationAsync(CancellationToken cancellationToken = default);
    bool IsCloudConfigEnabled();
    DateTime? GetLastSyncTime();
    string GetConfigurationPath();
}

public class ConfigurationSyncService : IConfigurationSyncService
{
    private readonly ICloudStorageService? _cloudStorageService;
    private readonly ILogger<ConfigurationSyncService> _logger;
    private readonly IConfiguration _configuration;
    private readonly string _localConfigPath;
    private readonly string _cloudConfigPath = "config/appsettings.json";
    private readonly string _cloudBackupPrefix = "config/backups/";
    private DateTime? _lastSyncTime;
    private readonly JsonSerializerOptions _jsonOptions;

    public ConfigurationSyncService(
        ICloudStorageService? cloudStorageService,
        ILogger<ConfigurationSyncService> logger,
        IConfiguration configuration)
    {
        _cloudStorageService = cloudStorageService;
        _logger = logger;
        _configuration = configuration;
        
        // Determine local config path
        _localConfigPath = Path.Combine(AppContext.BaseDirectory, "appsettings.json");
        
        _jsonOptions = new JsonSerializerOptions
        {
            WriteIndented = true,
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull,
            Converters = { new JsonStringEnumConverter() }
        };
    }

    public bool IsCloudConfigEnabled()
    {
        return _cloudStorageService != null && 
               _configuration.GetValue<bool>("CloudStorage:EnableConfigSync", false);
    }

    public DateTime? GetLastSyncTime() => _lastSyncTime;

    public string GetConfigurationPath() => _localConfigPath;

    public async Task<bool> SyncConfigurationAsync(CancellationToken cancellationToken = default)
    {
        if (!IsCloudConfigEnabled())
        {
            _logger.LogWarning("Cloud configuration sync is not enabled");
            return false;
        }

        try
        {
            _logger.LogInformation("Starting configuration sync with cloud storage");

            // Check if cloud config exists
            var cloudConfigExists = await _cloudStorageService!.FileExistsAsync(_cloudConfigPath, cancellationToken);
            
            if (!cloudConfigExists)
            {
                _logger.LogInformation("No cloud configuration found, uploading local configuration");
                return await UploadConfigurationAsync(cancellationToken);
            }

            // Get cloud config info
            var cloudInfo = await _cloudStorageService.GetFileInfoAsync(_cloudConfigPath, cancellationToken);
            
            // Get local config info
            var localInfo = new FileInfo(_localConfigPath);
            
            // Determine which is newer
            if (cloudInfo.Updated > localInfo.LastWriteTimeUtc)
            {
                _logger.LogInformation("Cloud configuration is newer, downloading");
                return await DownloadConfigurationAsync(cancellationToken);
            }
            else if (localInfo.LastWriteTimeUtc > cloudInfo.Updated)
            {
                _logger.LogInformation("Local configuration is newer, uploading");
                return await UploadConfigurationAsync(cancellationToken);
            }
            else
            {
                _logger.LogInformation("Configuration is already in sync");
                _lastSyncTime = DateTime.UtcNow;
                return true;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to sync configuration");
            return false;
        }
    }

    public async Task<bool> DownloadConfigurationAsync(CancellationToken cancellationToken = default)
    {
        if (_cloudStorageService == null)
        {
            _logger.LogWarning("Cloud storage service is not configured");
            return false;
        }

        try
        {
            _logger.LogInformation("Downloading configuration from cloud storage");

            // Backup current local config first
            var backupPath = $"{_localConfigPath}.backup-{DateTime.Now:yyyyMMddHHmmss}";
            if (File.Exists(_localConfigPath))
            {
                File.Copy(_localConfigPath, backupPath, true);
                _logger.LogInformation("Backed up current configuration to {Path}", backupPath);
            }

            // Download config from cloud
            var tempPath = Path.GetTempFileName();
            try
            {
                await _cloudStorageService.DownloadFileAsync(_cloudConfigPath, tempPath, cancellationToken: cancellationToken);
                
                // Validate the downloaded config
                var jsonContent = await File.ReadAllTextAsync(tempPath, cancellationToken);
                var configObj = JsonSerializer.Deserialize<JsonElement>(jsonContent);
                
                if (!configObj.TryGetProperty("Software", out _))
                {
                    _logger.LogWarning("Downloaded configuration is missing 'Software' section");
                    return false;
                }

                // Replace local config with downloaded one
                File.Copy(tempPath, _localConfigPath, true);
                
                _logger.LogInformation("Successfully downloaded and applied cloud configuration");
                _lastSyncTime = DateTime.UtcNow;
                
                // Clean up old backups (keep last 5)
                CleanupOldBackups();
                
                return true;
            }
            finally
            {
                if (File.Exists(tempPath))
                {
                    File.Delete(tempPath);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to download configuration from cloud");
            return false;
        }
    }

    public async Task<bool> UploadConfigurationAsync(CancellationToken cancellationToken = default)
    {
        if (_cloudStorageService == null)
        {
            _logger.LogWarning("Cloud storage service is not configured");
            return false;
        }

        try
        {
            if (!File.Exists(_localConfigPath))
            {
                _logger.LogWarning("Local configuration file not found: {Path}", _localConfigPath);
                return false;
            }

            _logger.LogInformation("Uploading configuration to cloud storage");

            // Create a backup in cloud before overwriting
            if (await _cloudStorageService.FileExistsAsync(_cloudConfigPath, cancellationToken))
            {
                await BackupConfigurationAsync(cancellationToken: cancellationToken);
            }

            // Upload current config
            await _cloudStorageService.UploadFileAsync(_localConfigPath, _cloudConfigPath, cancellationToken);
            
            _logger.LogInformation("Successfully uploaded configuration to cloud");
            _lastSyncTime = DateTime.UtcNow;
            
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to upload configuration to cloud");
            return false;
        }
    }

    public async Task<bool> BackupConfigurationAsync(string? backupName = null, CancellationToken cancellationToken = default)
    {
        if (_cloudStorageService == null)
        {
            _logger.LogWarning("Cloud storage service is not configured");
            return false;
        }

        try
        {
            var timestamp = DateTime.UtcNow.ToString("yyyyMMddHHmmss");
            var name = backupName ?? $"auto-{timestamp}";
            var backupPath = $"{_cloudBackupPrefix}appsettings-{name}.json";

            // Download current cloud config to temp file
            var tempPath = Path.GetTempFileName();
            try
            {
                await _cloudStorageService.DownloadFileAsync(_cloudConfigPath, tempPath, cancellationToken: cancellationToken);
                
                // Upload as backup
                await _cloudStorageService.UploadFileAsync(tempPath, backupPath, cancellationToken);
                
                _logger.LogInformation("Created configuration backup: {Path}", backupPath);
                return true;
            }
            finally
            {
                if (File.Exists(tempPath))
                {
                    File.Delete(tempPath);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to backup configuration");
            return false;
        }
    }

    public async Task<List<ConfigurationVersion>> GetConfigurationVersionsAsync(CancellationToken cancellationToken = default)
    {
        var versions = new List<ConfigurationVersion>();

        if (_cloudStorageService == null)
        {
            return versions;
        }

        try
        {
            var files = await _cloudStorageService.ListFilesAsync(_cloudBackupPrefix, cancellationToken);
            
            foreach (var file in files)
            {
                try
                {
                    var info = await _cloudStorageService.GetFileInfoAsync(file, cancellationToken);
                    var fileName = Path.GetFileName(file);
                    
                    versions.Add(new ConfigurationVersion
                    {
                        Id = file,
                        Name = fileName,
                        Created = info.Created?.DateTime ?? DateTime.MinValue,
                        Size = info.Size ?? 0,
                        IsBackup = true
                    });
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to get info for backup: {File}", file);
                }
            }

            // Add current version
            if (await _cloudStorageService.FileExistsAsync(_cloudConfigPath, cancellationToken))
            {
                var currentInfo = await _cloudStorageService.GetFileInfoAsync(_cloudConfigPath, cancellationToken);
                versions.Insert(0, new ConfigurationVersion
                {
                    Id = _cloudConfigPath,
                    Name = "Current Configuration",
                    Created = currentInfo.Updated?.DateTime ?? DateTime.MinValue,
                    Size = currentInfo.Size ?? 0,
                    IsBackup = false,
                    IsCurrent = true
                });
            }

            return versions.OrderByDescending(v => v.Created).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get configuration versions");
            return versions;
        }
    }

    public async Task<bool> RestoreConfigurationAsync(string versionId, CancellationToken cancellationToken = default)
    {
        if (_cloudStorageService == null)
        {
            _logger.LogWarning("Cloud storage service is not configured");
            return false;
        }

        try
        {
            _logger.LogInformation("Restoring configuration from version: {Version}", versionId);

            // Backup current config first
            await BackupConfigurationAsync("before-restore", cancellationToken);

            // Download the specified version
            var tempPath = Path.GetTempFileName();
            try
            {
                await _cloudStorageService.DownloadFileAsync(versionId, tempPath, cancellationToken: cancellationToken);
                
                // Validate the config
                var jsonContent = await File.ReadAllTextAsync(tempPath, cancellationToken);
                var configObj = JsonSerializer.Deserialize<JsonElement>(jsonContent);
                
                if (!configObj.TryGetProperty("Software", out _))
                {
                    _logger.LogWarning("Configuration version is missing 'Software' section");
                    return false;
                }

                // Upload as current config
                await _cloudStorageService.UploadFileAsync(tempPath, _cloudConfigPath, cancellationToken);
                
                // Update local config
                File.Copy(tempPath, _localConfigPath, true);
                
                _logger.LogInformation("Successfully restored configuration from version: {Version}", versionId);
                _lastSyncTime = DateTime.UtcNow;
                
                return true;
            }
            finally
            {
                if (File.Exists(tempPath))
                {
                    File.Delete(tempPath);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to restore configuration");
            return false;
        }
    }

    private void CleanupOldBackups()
    {
        try
        {
            var backupFiles = Directory.GetFiles(
                Path.GetDirectoryName(_localConfigPath)!, 
                "appsettings.json.backup-*")
                .OrderByDescending(f => File.GetCreationTimeUtc(f))
                .Skip(5);

            foreach (var oldBackup in backupFiles)
            {
                File.Delete(oldBackup);
                _logger.LogDebug("Deleted old backup: {Path}", oldBackup);
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to cleanup old backups");
        }
    }
}

public class ConfigurationVersion
{
    public string Id { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public DateTime Created { get; set; }
    public ulong Size { get; set; }
    public bool IsBackup { get; set; }
    public bool IsCurrent { get; set; }
}