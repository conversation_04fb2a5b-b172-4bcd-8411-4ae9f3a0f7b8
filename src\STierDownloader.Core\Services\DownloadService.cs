using System.Security.Cryptography;
using Microsoft.Extensions.Logging;

namespace STierDownloader.Core.Services;

public interface IDownloadService
{
    Task<string> DownloadFileAsync(string url, string destinationPath, IProgress<double>? progress = null, CancellationToken cancellationToken = default);
    Task<bool> VerifyChecksumAsync(string filePath, string expectedChecksum);
}

public class DownloadService : IDownloadService
{
    private readonly ILogger<DownloadService> _logger;
    private readonly HttpClient _httpClient;

    public DownloadService(ILogger<DownloadService> logger, HttpClient httpClient)
    {
        _logger = logger;
        _httpClient = httpClient;
    }

    public async Task<string> DownloadFileAsync(string url, string destinationPath, IProgress<double>? progress = null, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Starting download from {Url} to {Path}", url, destinationPath);

        try
        {
            using var response = await _httpClient.GetAsync(url, HttpCompletionOption.ResponseHeadersRead, cancellationToken);
            response.EnsureSuccessStatusCode();

            var totalBytes = response.Content.Headers.ContentLength ?? -1L;
            var canReportProgress = totalBytes != -1 && progress != null;

            Directory.CreateDirectory(Path.GetDirectoryName(destinationPath)!);

            using var contentStream = await response.Content.ReadAsStreamAsync(cancellationToken);
            using var fileStream = new FileStream(destinationPath, FileMode.Create, FileAccess.Write, FileShare.None, 8192, true);

            var buffer = new byte[8192];
            var totalBytesRead = 0L;
            int bytesRead;

            while ((bytesRead = await contentStream.ReadAsync(buffer, 0, buffer.Length, cancellationToken)) != 0)
            {
                await fileStream.WriteAsync(buffer, 0, bytesRead, cancellationToken);
                totalBytesRead += bytesRead;

                if (canReportProgress)
                {
                    var progressPercentage = (double)totalBytesRead / totalBytes * 100;
                    progress!.Report(progressPercentage);
                }
            }

            _logger.LogInformation("Download completed: {Path}", destinationPath);
            return destinationPath;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Download failed for {Url}", url);
            throw;
        }
    }

    public async Task<bool> VerifyChecksumAsync(string filePath, string expectedChecksum)
    {
        if (string.IsNullOrEmpty(expectedChecksum))
            return true;

        try
        {
            using var stream = File.OpenRead(filePath);
            using var sha256 = SHA256.Create();
            var hashBytes = await sha256.ComputeHashAsync(stream);
            var actualChecksum = BitConverter.ToString(hashBytes).Replace("-", "").ToLowerInvariant();
            
            var isValid = actualChecksum.Equals(expectedChecksum, StringComparison.OrdinalIgnoreCase);
            
            if (!isValid)
            {
                _logger.LogWarning("Checksum mismatch for {File}. Expected: {Expected}, Actual: {Actual}", 
                    filePath, expectedChecksum, actualChecksum);
            }

            return isValid;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to verify checksum for {File}", filePath);
            return false;
        }
    }
}