using System;
using System.Globalization;
using System.Windows.Data;

namespace STierDownloader.UI.Converters;

public class NullToBooleanConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        // Returns true if value is not null, false if it is null
        // Can be inverted by passing "true" as parameter
        bool invert = parameter?.ToString()?.ToLower() == "true";
        bool result = value != null;
        
        return invert ? !result : result;
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}