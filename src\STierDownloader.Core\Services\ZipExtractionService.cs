using System.IO.Compression;
using Microsoft.Extensions.Logging;
using STierDownloader.Core.Models;

namespace STierDownloader.Core.Services;

public interface IZipExtractionService
{
    Task<string> ExtractZipAsync(string zipPath, string? targetDirectory = null, IProgress<double>? progress = null);
    Task CleanupExtractedFilesAsync(string extractedPath);
    Task<string?> FindInstallerInExtractedFiles(string extractedPath, Software software);
}

public class ZipExtractionService : IZipExtractionService
{
    private readonly ILogger<ZipExtractionService> _logger;
    
    public ZipExtractionService(ILogger<ZipExtractionService> logger)
    {
        _logger = logger;
    }
    
    public async Task<string> ExtractZipAsync(string zipPath, string? targetDirectory = null, IProgress<double>? progress = null)
    {
        if (!File.Exists(zipPath))
        {
            throw new FileNotFoundException($"Zip file not found: {zipPath}");
        }
        
        // Generate extraction directory if not provided
        if (string.IsNullOrEmpty(targetDirectory))
        {
            var tempDir = Environment.ExpandEnvironmentVariables("%TEMP%");
            var zipName = Path.GetFileNameWithoutExtension(zipPath);
            targetDirectory = Path.Combine(tempDir, "STierDownloader", "Extracted", zipName + "_" + Guid.NewGuid().ToString("N").Substring(0, 8));
        }
        
        _logger.LogInformation("Extracting {ZipFile} to {TargetDir}", zipPath, targetDirectory);
        
        try
        {
            // Ensure target directory exists
            Directory.CreateDirectory(targetDirectory);
            
            // Extract with progress reporting
            await Task.Run(() =>
            {
                using var archive = ZipFile.OpenRead(zipPath);
                var totalEntries = archive.Entries.Count;
                var extractedCount = 0;
                
                foreach (var entry in archive.Entries)
                {
                    var destinationPath = Path.Combine(targetDirectory, entry.FullName);
                    
                    // Create directory if it's a folder entry
                    if (string.IsNullOrEmpty(entry.Name))
                    {
                        Directory.CreateDirectory(destinationPath);
                    }
                    else
                    {
                        // Ensure parent directory exists
                        var parentDir = Path.GetDirectoryName(destinationPath);
                        if (!string.IsNullOrEmpty(parentDir))
                        {
                            Directory.CreateDirectory(parentDir);
                        }
                        
                        // Extract file
                        entry.ExtractToFile(destinationPath, overwrite: true);
                    }
                    
                    extractedCount++;
                    progress?.Report((double)extractedCount / totalEntries * 100);
                }
            });
            
            _logger.LogInformation("Successfully extracted {ZipFile} to {TargetDir}", zipPath, targetDirectory);
            return targetDirectory;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to extract {ZipFile}", zipPath);
            
            // Clean up partial extraction
            if (Directory.Exists(targetDirectory))
            {
                try
                {
                    Directory.Delete(targetDirectory, recursive: true);
                }
                catch { }
            }
            
            throw;
        }
    }
    
    public async Task CleanupExtractedFilesAsync(string extractedPath)
    {
        if (!Directory.Exists(extractedPath))
        {
            _logger.LogWarning("Extracted path does not exist: {Path}", extractedPath);
            return;
        }
        
        try
        {
            _logger.LogInformation("Cleaning up extracted files at {Path}", extractedPath);
            
            await Task.Run(() =>
            {
                // Try to delete the directory multiple times in case files are still in use
                for (int attempt = 0; attempt < 3; attempt++)
                {
                    try
                    {
                        Directory.Delete(extractedPath, recursive: true);
                        _logger.LogInformation("Successfully cleaned up {Path}", extractedPath);
                        return;
                    }
                    catch (Exception ex)
                    {
                        if (attempt == 2)
                        {
                            _logger.LogWarning(ex, "Failed to cleanup {Path} after 3 attempts", extractedPath);
                        }
                        else
                        {
                            // Wait a bit and retry
                            Thread.Sleep(1000);
                        }
                    }
                }
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to cleanup extracted files at {Path}", extractedPath);
        }
    }
    
    public async Task<string?> FindInstallerInExtractedFiles(string extractedPath, Software software)
    {
        if (!Directory.Exists(extractedPath))
        {
            _logger.LogWarning("Extracted path does not exist: {Path}", extractedPath);
            return null;
        }
        
        return await Task.Run(() =>
        {
            try
            {
                // For Niagara/Vykon, look for platform-specific installer
                if (software.Type == SoftwareType.TridiumNiagara)
                {
                    // Prefer 64-bit installer
                    var installer64 = Path.Combine(extractedPath, "Installer_x64.exe");
                    if (File.Exists(installer64))
                    {
                        _logger.LogInformation("Found 64-bit installer: {Path}", installer64);
                        return installer64;
                    }
                    
                    // Fall back to 32-bit if needed
                    var installer32 = Path.Combine(extractedPath, "Installer_x86.exe");
                    if (File.Exists(installer32))
                    {
                        _logger.LogInformation("Found 32-bit installer: {Path}", installer32);
                        return installer32;
                    }
                    
                    // Generic installer name
                    var genericInstaller = Path.Combine(extractedPath, "Installer.exe");
                    if (File.Exists(genericInstaller))
                    {
                        _logger.LogInformation("Found generic installer: {Path}", genericInstaller);
                        return genericInstaller;
                    }
                }
                
                // Search for any .exe or .msi installer
                var installerPatterns = new[] { "setup*.exe", "install*.exe", "*.msi", "*setup.exe", "*installer.exe" };
                
                foreach (var pattern in installerPatterns)
                {
                    var files = Directory.GetFiles(extractedPath, pattern, SearchOption.TopDirectoryOnly);
                    if (files.Length > 0)
                    {
                        _logger.LogInformation("Found installer matching pattern {Pattern}: {Path}", pattern, files[0]);
                        return files[0];
                    }
                }
                
                // Look in common subdirectories
                var subDirs = new[] { "setup", "installer", "install", "bin", "x64", "x86" };
                foreach (var subDir in subDirs)
                {
                    var subPath = Path.Combine(extractedPath, subDir);
                    if (Directory.Exists(subPath))
                    {
                        foreach (var pattern in installerPatterns)
                        {
                            var files = Directory.GetFiles(subPath, pattern, SearchOption.TopDirectoryOnly);
                            if (files.Length > 0)
                            {
                                _logger.LogInformation("Found installer in subdirectory {SubDir}: {Path}", subDir, files[0]);
                                return files[0];
                            }
                        }
                    }
                }
                
                _logger.LogWarning("No installer found in extracted files at {Path}", extractedPath);
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching for installer in {Path}", extractedPath);
                return null;
            }
        });
    }
}