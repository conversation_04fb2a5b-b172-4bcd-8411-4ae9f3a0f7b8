using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Input;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using STierDownloader.Core.Models;
using STierDownloader.Core.Services;
using STierDownloader.UI.Views;

namespace STierDownloader.UI.ViewModels;

public partial class MainViewModel : ObservableObject
{
    private readonly IUnifiedDownloadService _downloadService;
    private readonly IInstallerService _installerService;
    private readonly ILogger<MainViewModel> _logger;
    private readonly IServiceProvider _serviceProvider;
    private readonly ICloudSoftwareService? _cloudSoftwareService;
    private readonly IInstallationDetectionService? _detectionService;

    [ObservableProperty]
    private ObservableCollection<SoftwareViewModel> _softwareList = new();

    [ObservableProperty]
    private SoftwareViewModel? _selectedSoftware;

    [ObservableProperty]
    private bool _isLoading;

    [ObservableProperty]
    private string _statusMessage = "Ready";

    public MainViewModel(
        IUnifiedDownloadService downloadService,
        IInstallerService installerService,
        ILogger<MainViewModel> logger,
        IServiceProvider serviceProvider,
        ICloudSoftwareService? cloudSoftwareService,
        IInstallationDetectionService? detectionService)
    {
        _downloadService = downloadService;
        _installerService = installerService;
        _logger = logger;
        _serviceProvider = serviceProvider;
        _cloudSoftwareService = cloudSoftwareService;
        _detectionService = detectionService;

        // Load software from cloud on startup
        Task.Run(async () => await LoadSoftwareFromCloudAsync());
    }

    private async Task LoadSoftwareFromCloudAsync()
    {
        try
        {
            await Application.Current.Dispatcher.InvokeAsync(() =>
            {
                IsLoading = true;
                StatusMessage = "Loading software from cloud...";
                SoftwareList.Clear();
            });

            if (_cloudSoftwareService == null)
            {
                await Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    StatusMessage = "Cloud storage not configured";
                    IsLoading = false;
                });
                return;
            }

            // Load software directly from bucket contents
            var availableSoftware = await _cloudSoftwareService.GetAvailableSoftwareAsync();
            
            await Application.Current.Dispatcher.InvokeAsync(() =>
            {
                foreach (var software in availableSoftware)
                {
                    var softwareLogger = _serviceProvider.GetService<ILogger<SoftwareViewModel>>();
                    var viewModel = new SoftwareViewModel(software, _downloadService, _installerService, _detectionService, softwareLogger);
                    SoftwareList.Add(viewModel);
                }
                
                if (SoftwareList.Count == 0)
                {
                    StatusMessage = "No software found. Upload software to the cloud storage bucket";
                    _logger.LogWarning("No software items found in cloud storage");
                }
                else
                {
                    StatusMessage = $"Loaded {SoftwareList.Count} software items from cloud";
                    _logger.LogInformation("Loaded {Count} software items from cloud storage", SoftwareList.Count);
                }
                
                IsLoading = false;
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to load software from cloud");
            await Application.Current.Dispatcher.InvokeAsync(() =>
            {
                StatusMessage = "Failed to load software from cloud";
                IsLoading = false;
            });
        }
    }

    [RelayCommand]
    private async Task RefreshAllAsync()
    {
        // Reload software from cloud
        await LoadSoftwareFromCloudAsync();
        
        // Then check status of each
        try
        {
            IsLoading = true;
            StatusMessage = "Checking software status...";
            
            foreach (var software in SoftwareList)
            {
                await software.CheckStatusAsync();
            }
            
            StatusMessage = "Refresh completed";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to refresh software status");
            StatusMessage = "Refresh failed";
        }
        finally
        {
            IsLoading = false;
        }
    }

    [RelayCommand]
    private async Task DownloadAllAsync()
    {
        IsLoading = true;
        StatusMessage = "Downloading all software...";

        try
        {
            var downloadTasks = SoftwareList
                .Where(s => !s.IsDownloaded)
                .Select(s => s.DownloadAsync());

            await Task.WhenAll(downloadTasks);
            
            StatusMessage = "All downloads completed";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to download all software");
            StatusMessage = "Some downloads failed";
        }
        finally
        {
            IsLoading = false;
        }
    }

    [RelayCommand]
    private async Task InstallAllAsync()
    {
        IsLoading = true;
        StatusMessage = "Installing all software...";

        try
        {
            foreach (var software in SoftwareList.Where(s => s.IsDownloaded && !s.IsInstalled))
            {
                await software.InstallAsync();
            }
            
            StatusMessage = "All installations completed";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to install all software");
            StatusMessage = "Some installations failed";
        }
        finally
        {
            IsLoading = false;
        }
    }

    [RelayCommand]
    private void OpenUploadWindow()
    {
        try
        {
            var uploadViewModel = _serviceProvider.GetRequiredService<UploadViewModel>();
            var uploadWindow = new UploadWindow(uploadViewModel);
            uploadWindow.ShowDialog();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to open upload window");
            StatusMessage = "Failed to open upload window - check GCS configuration";
        }
    }
    
    /// <summary>
    /// Cancels all ongoing operations in preparation for application shutdown
    /// </summary>
    public void CancelAllOperations()
    {
        try
        {
            // Cancel all downloads/installations in progress
            foreach (var software in SoftwareList)
            {
                software.CancelOperation();
            }
            
            _logger.LogInformation("All operations cancelled for shutdown");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error cancelling operations during shutdown");
        }
    }

}