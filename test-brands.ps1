# Test script to verify brand management service

# Get the brands configuration path
$appDataPath = [Environment]::GetFolderPath([System.Environment+SpecialFolder]::LocalApplicationData)
$brandsFile = Join-Path $appDataPath "STierDownloader\Config\niagara-brands.json"

Write-Host "Brand Configuration File: $brandsFile" -ForegroundColor Cyan

# Check if file exists
if (Test-Path $brandsFile) {
    Write-Host "Brands configuration file exists" -ForegroundColor Green
    
    # Read and display the content
    $content = Get-Content $brandsFile -Raw | ConvertFrom-Json
    
    Write-Host ""
    Write-Host "Configured Brands:" -ForegroundColor Yellow
    foreach ($brand in $content.Brands) {
        $type = if ($brand.IsBuiltIn) { "[Built-in]" } else { "[Custom]" }
        Write-Host "  - $($brand.DisplayName) $type -> $($brand.InstallPath)" -ForegroundColor White
    }
    
    Write-Host ""
    Write-Host "Total Brands: $($content.Brands.Count)" -ForegroundColor Cyan
    Write-Host "Last Modified: $($content.LastModified)" -ForegroundColor Gray
} else {
    Write-Host "Brands configuration file not found" -ForegroundColor Red
    Write-Host "  Creating default configuration..." -ForegroundColor Yellow
    
    # Create directory
    $configDir = Split-Path $brandsFile -Parent
    New-Item -ItemType Directory -Path $configDir -Force | Out-Null
    
    Write-Host "  Directory created: $configDir" -ForegroundColor Gray
    Write-Host "  The configuration will be created when the application runs." -ForegroundColor Gray
}

Write-Host ""
Write-Host "Press Enter to exit..."
Read-Host