namespace STierDownloader.Core.Services;

/// <summary>
/// A stream wrapper that tracks read progress for upload operations
/// </summary>
public class ProgressStream : Stream
{
    private readonly Stream _innerStream;
    private readonly long _totalLength;
    private readonly Action<long>? _progressCallback;
    private long _bytesRead;

    public ProgressStream(Stream innerStream, long totalLength, Action<long>? progressCallback)
    {
        _innerStream = innerStream ?? throw new ArgumentNullException(nameof(innerStream));
        _totalLength = totalLength;
        _progressCallback = progressCallback;
        _bytesRead = 0;
    }

    public override bool CanRead => _innerStream.CanRead;
    public override bool CanSeek => _innerStream.CanSeek;
    public override bool CanWrite => _innerStream.CanWrite;
    public override long Length => _innerStream.Length;
    public override long Position
    {
        get => _innerStream.Position;
        set => _innerStream.Position = value;
    }

    public override void Flush()
    {
        _innerStream.Flush();
    }

    public override int Read(byte[] buffer, int offset, int count)
    {
        var bytesRead = _innerStream.Read(buffer, offset, count);
        if (bytesRead > 0)
        {
            _bytesRead += bytesRead;
            _progressCallback?.Invoke(_bytesRead);
        }
        return bytesRead;
    }

    public override async Task<int> ReadAsync(byte[] buffer, int offset, int count, CancellationToken cancellationToken)
    {
        var bytesRead = await _innerStream.ReadAsync(buffer, offset, count, cancellationToken);
        if (bytesRead > 0)
        {
            _bytesRead += bytesRead;
            _progressCallback?.Invoke(_bytesRead);
        }
        return bytesRead;
    }

    public override long Seek(long offset, SeekOrigin origin)
    {
        return _innerStream.Seek(offset, origin);
    }

    public override void SetLength(long value)
    {
        _innerStream.SetLength(value);
    }

    public override void Write(byte[] buffer, int offset, int count)
    {
        _innerStream.Write(buffer, offset, count);
    }

    protected override void Dispose(bool disposing)
    {
        if (disposing)
        {
            _innerStream?.Dispose();
        }
        base.Dispose(disposing);
    }
}