namespace STierDownloader.Core.Models;

public class Software
{
    public Guid Id { get; set; } = Guid.NewGuid();
    public string Name { get; set; } = string.Empty;
    public string Version { get; set; } = string.Empty;
    public string DownloadUrl { get; set; } = string.Empty;
    public string InstallerPath { get; set; } = string.Empty;
    public SoftwareType Type { get; set; }
    public string? InstallArguments { get; set; }
    public string? UninstallCommand { get; set; }
    public bool RequiresElevation { get; set; } = true;
    public long FileSize { get; set; }
    public string? ChecksumSha256 { get; set; }
    public DateTime? LastUpdated { get; set; }
    public bool IsInstalled { get; set; }
    public string? InstalledVersion { get; set; }
    public string? MsiProductCode { get; set; }
    public string? ExpectedInstallPath { get; set; }
    public string? MainExecutable { get; set; }
    
    // Niagara-specific properties
    public NiagaraVariant? NiagaraVariant { get; set; }
    public bool IsZipPackage { get; set; }
    public string? ExtractedPath { get; set; }
    public bool CleanupAfterInstall { get; set; } = true;
    
    // Cloud Storage properties
    public StorageLocation StorageLocation { get; set; } = StorageLocation.Http;
    public string? CloudObjectName { get; set; }
    public string? CloudBucketOverride { get; set; }
    public bool UseSignedUrl { get; set; } = false;
    public int SignedUrlExpirationHours { get; set; } = 24;
}

public enum StorageLocation
{
    Http,
    GoogleCloudStorage,
    LocalFile,
    AzureBlob,
    AmazonS3
}

public enum SoftwareType
{
    TridiumNiagara,
    VpnClient,
    HvacProgrammingTool,
    Driver,
    Utility,
    Framework
}

public enum NiagaraVariant
{
    Standard,
    Vykon,
    Honeywell,
    Distech,
    Lynxspring,
    Other
}