# Admin Upload Guide

## Overview

The Admin Upload feature allows administrators to easily upload software installers to Google Cloud Storage directly from the application.

## Accessing the Upload Window

1. Run the S-Tier Downloader as Administrator
2. Click the **"Admin Upload"** button (purple button in the toolbar)
3. The Upload Window will open

## Upload Process

### Step 1: Select File

1. Click **"Browse..."** to select your installer file
2. Supported formats: `.exe`, `.msi`, `.zip`, `.cab`
3. The app will auto-detect:
   - Software name from filename
   - Appropriate install arguments
   - File type

### Step 2: Configure Metadata

Fill in the software information:

- **Software Name**: Display name for the software
- **Version**: Version number (e.g., "4.13.1")
- **Type**: Select category (Niagara, VPN, HVAC Tool, etc.)
- **Cloud Path**: Auto-generated path in GCS (editable)

### Step 3: Installation Settings

Configure how the software will be installed:

- **Install Arguments**: 
  - For MSI: `/quiet /norestart`
  - For EXE: `/S /v/qn`
  - Custom arguments as needed

- **Uninstall Command**: 
  - For MSI: `msiexec /x {PRODUCT_CODE} /quiet`
  - Leave blank if unknown

- **Options**:
  - ✅ **Requires Admin**: If installation needs elevation
  - ✅ **Generate Checksum**: Auto-calculate SHA-256
  - ✅ **Use Signed URLs**: For temporary download links

### Step 4: Upload

1. Click **"Upload to Cloud Storage"**
2. Watch the progress bar
3. Configuration JSON is automatically copied to clipboard
4. Add the JSON to `appsettings.json`

## Cloud Path Structure

Files are organized by category:

```
software/
├── niagara/          # Tridium Niagara installers
├── vpn/              # VPN clients
├── hvac-tools/       # HVAC programming tools
├── drivers/          # Device drivers
├── frameworks/       # .NET, Java runtimes
└── utilities/        # Other utilities
```

## Quick Actions

### List Bucket Contents
- Shows all files currently in the bucket
- Useful for checking what's already uploaded

### Generate Signed URL
- Creates a temporary download link
- Valid for specified hours (default: 24)
- URL is copied to clipboard
- Share with users who don't have GCS access

## After Upload

### 1. Configuration Added to Clipboard

The app generates JSON configuration like:

```json
{
  "Name": "Tridium Niagara 4",
  "Version": "4.13.1",
  "Type": "TridiumNiagara",
  "StorageLocation": "GoogleCloudStorage",
  "CloudObjectName": "software/niagara/niagara-4.13.1.exe",
  "UseSignedUrl": false,
  "InstallArguments": "/S /v/qn",
  "ChecksumSha256": "abc123...",
  "RequiresElevation": true
}
```

### 2. Add to appsettings.json

1. Open `appsettings.json`
2. Find the `"Software"` array
3. Add the new configuration
4. Save the file

### 3. Restart Application

For new software to appear in the main list, restart the application.

## Best Practices

### File Naming
- Use descriptive names: `software-version-platform.ext`
- Example: `niagara-4.13.1-windows-x64.exe`
- Avoid spaces in filenames

### Version Management
- Always include version in filename
- Keep old versions for rollback
- Document breaking changes

### Security
- Always generate checksums
- Test installers before upload
- Verify silent install arguments
- Document any special requirements

### Organization
- Use consistent folder structure
- Group related software together
- Clean up old/obsolete versions periodically

## Bulk Upload

For multiple files:

1. Upload each file individually
2. Copy all generated configurations
3. Add them all to `appsettings.json` at once
4. Restart application once

## Command Line Alternative

You can also use `gsutil` for bulk uploads:

```bash
# Upload entire folder
gsutil -m cp -r ./installers/* gs://s-tier-software/software/

# Upload with metadata
gsutil -h "Cache-Control:no-cache" cp installer.exe gs://s-tier-software/software/tools/
```

## Troubleshooting

### Upload Fails

1. Check internet connection
2. Verify GCS credentials in `gcs-credentials.json`
3. Ensure bucket exists and has write permissions
4. Check file isn't too large (GCS limit: 5TB)

### Can't See Upload Button

- Ensure you have GCS configured in `appsettings.json`
- Check that credentials file exists
- Restart application after configuration changes

### Generated Config Missing Fields

- Ensure all required fields are filled
- Software Name and Version are mandatory
- Checksum generates automatically if enabled

### Files Don't Appear in Main List

1. Add configuration to `appsettings.json`
2. Restart the application
3. Click "Refresh All" button

## Security Notes

⚠️ **Admin upload requires elevated privileges**

- Only trusted administrators should have access
- Upload window shows in title: "Admin"
- All uploads are logged
- Consider separate admin credentials for production

## Support

For issues with upload functionality:
1. Check `%LOCALAPPDATA%\STierDownloader\Logs`
2. Verify GCS bucket permissions
3. Test with a small file first
4. Check quota and billing in GCP Console