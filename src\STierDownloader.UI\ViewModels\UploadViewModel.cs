using System.Collections.ObjectModel;
using System.IO;
using System.Windows.Input;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Win32;
using STierDownloader.Core.Models;
using STierDownloader.Core.Services;
using STierDownloader.UI.Views;
using System.Text.Json;
using System.Text.Json.Nodes;

namespace STierDownloader.UI.ViewModels;

public partial class UploadViewModel : ObservableObject
{
    private readonly ICloudStorageService? _cloudStorageService;
    private readonly ITemplateManagementService? _templateService;
    private readonly ILogger<UploadViewModel> _logger;
    private readonly IServiceProvider _serviceProvider;

    [ObservableProperty]
    private string _localFilePath = string.Empty;

    [ObservableProperty]
    private string _fileName = string.Empty;

    [ObservableProperty]
    private string _softwareName = string.Empty;

    [ObservableProperty]
    private string _version = string.Empty;

    [ObservableProperty]
    private SoftwareType _selectedType = SoftwareType.Utility;

    [ObservableProperty]
    private string _cloudPath = string.Empty;

    [ObservableProperty]
    private string _installArguments = string.Empty;

    [ObservableProperty]
    private string _uninstallCommand = string.Empty;

    [ObservableProperty]
    private string _expectedInstallPath = string.Empty;

    [ObservableProperty]
    private string _mainExecutable = string.Empty;

    [ObservableProperty]
    private bool _requiresElevation = true;

    [ObservableProperty]
    private string _checksumSha256 = string.Empty;

    [ObservableProperty]
    private bool _isUploading;

    [ObservableProperty]
    private double _uploadProgress;

    [ObservableProperty]
    private string _statusMessage = "Ready to upload";
    
    [ObservableProperty]
    private string _uploadSpeed = "";
    
    [ObservableProperty]
    private string _timeRemaining = "";
    
    [ObservableProperty]
    private string _bytesUploaded = "";
    
    [ObservableProperty]
    private long _totalBytes;
    
    [ObservableProperty]
    private string _timeElapsed = "";

    [ObservableProperty]
    private ObservableCollection<string> _recentUploads = new();

    [ObservableProperty]
    private bool _generateChecksum = true;

    [ObservableProperty]
    private bool _useSignedUrl = false;

    [ObservableProperty]
    private int _signedUrlHours = 24;
    
    [ObservableProperty]
    private ObservableCollection<SoftwareTemplate> _availableTemplates = new();
    
    [ObservableProperty]
    private SoftwareTemplate? _selectedTemplate;
    
    [ObservableProperty]
    private bool _isZipPackage;
    
    [ObservableProperty]
    private bool _cleanupAfterInstall = true;

    public IEnumerable<SoftwareType> SoftwareTypes => Enum.GetValues<SoftwareType>();

    public UploadViewModel(
        ICloudStorageService? cloudStorageService,
        ITemplateManagementService? templateService,
        ILogger<UploadViewModel> logger,
        IServiceProvider serviceProvider)
    {
        _cloudStorageService = cloudStorageService;
        _templateService = templateService;
        _logger = logger;
        _serviceProvider = serviceProvider;
        
        // Load templates
        Task.Run(async () => await LoadTemplatesAsync());
        
        // Set default cloud path
        CloudPath = "software/";
    }
    
    private async Task LoadTemplatesAsync()
    {
        if (_templateService == null) return;
        
        try
        {
            var templates = await _templateService.GetTemplatesAsync();
            
            await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
            {
                AvailableTemplates.Clear();
                foreach (var template in templates.OrderBy(t => t.TemplateName))
                {
                    AvailableTemplates.Add(template);
                }
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to load templates");
        }
    }

    [RelayCommand]
    private void BrowseInstalledExe()
    {
        var openFileDialog = new OpenFileDialog
        {
            Title = "Select Installed Executable - This will auto-fill the installation paths",
            Filter = "Executable Files|*.exe|All Files|*.*",
            CheckFileExists = true,
            InitialDirectory = @"C:\Program Files"
        };

        if (openFileDialog.ShowDialog() == true)
        {
            var selectedExePath = openFileDialog.FileName;
            
            // Extract the directory path
            ExpectedInstallPath = Path.GetDirectoryName(selectedExePath) ?? "";
            
            // Extract just the executable filename
            MainExecutable = Path.GetFileName(selectedExePath);
            
            // Try to get version info from the selected executable
            try
            {
                var versionInfo = System.Diagnostics.FileVersionInfo.GetVersionInfo(selectedExePath);
                
                // Auto-fill version if empty
                if (string.IsNullOrEmpty(Version) && !string.IsNullOrEmpty(versionInfo.FileVersion))
                {
                    Version = versionInfo.FileVersion;
                }
                
                // Auto-fill software name if empty
                if (string.IsNullOrEmpty(SoftwareName) && !string.IsNullOrEmpty(versionInfo.ProductName))
                {
                    SoftwareName = versionInfo.ProductName;
                }
                else if (string.IsNullOrEmpty(SoftwareName) && !string.IsNullOrEmpty(versionInfo.CompanyName))
                {
                    // Try to use company name and product name
                    var productName = Path.GetFileNameWithoutExtension(MainExecutable);
                    SoftwareName = $"{versionInfo.CompanyName} {productName}".Trim();
                }
                
                _logger.LogInformation("Auto-filled from executable: Path={Path}, Exe={Exe}, Version={Version}", 
                    ExpectedInstallPath, MainExecutable, Version);
                
                StatusMessage = $"Auto-filled from: {MainExecutable}";
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to extract version info from: {Path}", selectedExePath);
                StatusMessage = "Paths filled, but couldn't extract version info";
            }
        }
    }

    [RelayCommand]
    private void BrowseFile()
    {
        var openFileDialog = new OpenFileDialog
        {
            Title = "Select Software Installer",
            Filter = "Installer Files|*.exe;*.msi;*.zip;*.cab|All Files|*.*",
            CheckFileExists = true
        };

        if (openFileDialog.ShowDialog() == true)
        {
            LocalFilePath = openFileDialog.FileName;
            FileName = Path.GetFileName(LocalFilePath);
            StatusMessage = $"Selected: {FileName}";
            
            // Suggest a template based on the file name
            if (_templateService != null)
            {
                var suggestedTemplate = _templateService.SuggestTemplateForFile(FileName);
                if (suggestedTemplate != null)
                {
                    SelectedTemplate = AvailableTemplates.FirstOrDefault(t => t.Id == suggestedTemplate.Id);
                    if (SelectedTemplate != null)
                    {
                        ApplyTemplate();
                        StatusMessage = $"Selected: {FileName} (Template: {suggestedTemplate.TemplateName})";
                    }
                }
            }
            
            // Set default cloud path if not set by template
            if (string.IsNullOrEmpty(CloudPath))
            {
                CloudPath = $"software/{FileName}";
            }

            // Generate checksum if requested
            if (GenerateChecksum)
            {
                _ = CalculateChecksumAsync();
            }
        }
    }


    partial void OnFileNameChanged(string value)
    {
        // Auto-detect if it's a zip package
        if (!string.IsNullOrEmpty(value))
        {
            IsZipPackage = Path.GetExtension(value).Equals(".zip", StringComparison.OrdinalIgnoreCase);
        }
    }
    
    [RelayCommand]
    private async Task OpenTemplateManagementAsync()
    {
        try
        {
            // Try to get the window from DI, or create it directly if that fails
            TemplateManagementWindow? templateWindow = null;
            
            try
            {
                templateWindow = _serviceProvider.GetService<TemplateManagementWindow>();
            }
            catch
            {
                // Fallback to direct creation
                var templateLogger = _serviceProvider.GetService<ILogger<TemplateManagementWindow>>() 
                    ?? Microsoft.Extensions.Logging.Abstractions.NullLogger<TemplateManagementWindow>.Instance;
                templateWindow = new TemplateManagementWindow(_templateService, templateLogger);
            }
            
            if (templateWindow == null)
            {
                // Last resort - create with minimal dependencies
                var templateLogger = Microsoft.Extensions.Logging.Abstractions.NullLogger<TemplateManagementWindow>.Instance;
                templateWindow = new TemplateManagementWindow(_templateService, templateLogger);
            }
            
            if (templateWindow.ShowDialog() == true)
            {
                // Reload templates after management window closes
                await LoadTemplatesAsync();
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to open template management window: {Message}", ex.Message);
            StatusMessage = $"Failed to open template management: {ex.Message}";
        }
    }

    private async Task CalculateChecksumAsync()
    {
        if (!File.Exists(LocalFilePath))
            return;

        try
        {
            StatusMessage = "Calculating checksum...";
            
            await Task.Run(async () =>
            {
                using var stream = File.OpenRead(LocalFilePath);
                using var sha256 = System.Security.Cryptography.SHA256.Create();
                var hashBytes = await sha256.ComputeHashAsync(stream);
                ChecksumSha256 = BitConverter.ToString(hashBytes).Replace("-", "").ToLowerInvariant();
            });

            StatusMessage = "Checksum calculated";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to calculate checksum");
            StatusMessage = "Failed to calculate checksum";
        }
    }

    [RelayCommand]
    private async Task UploadAsync()
    {
        if (_cloudStorageService == null)
        {
            StatusMessage = "Cloud storage service not configured";
            return;
        }

        if (string.IsNullOrEmpty(LocalFilePath) || !File.Exists(LocalFilePath))
        {
            StatusMessage = "Please select a valid file";
            return;
        }

        if (string.IsNullOrEmpty(SoftwareName) || string.IsNullOrEmpty(Version))
        {
            StatusMessage = "Please fill in software name and version";
            return;
        }

        try
        {
            IsUploading = true;
            UploadProgress = 0;
            StatusMessage = $"Uploading {FileName}...";

            // Prepare metadata
            var metadata = new Dictionary<string, string>
            {
                ["software-name"] = SoftwareName,
                ["software-version"] = Version,
                ["software-type"] = SelectedType.ToString(),
                ["install-arguments"] = InstallArguments ?? "",
                ["requires-elevation"] = RequiresElevation.ToString()
            };
            
            if (!string.IsNullOrEmpty(ChecksumSha256))
                metadata["checksum-sha256"] = ChecksumSha256;
            
            if (!string.IsNullOrEmpty(UninstallCommand))
                metadata["uninstall-command"] = UninstallCommand;
            
            if (!string.IsNullOrEmpty(ExpectedInstallPath))
                metadata["expected-install-path"] = ExpectedInstallPath;
            
            if (!string.IsNullOrEmpty(MainExecutable))
                metadata["main-executable"] = MainExecutable;
            
            // Add zip package metadata
            if (IsZipPackage)
            {
                metadata["is-zip-package"] = "true";
                metadata["cleanup-after-install"] = CleanupAfterInstall.ToString();
            }
            
            // Add template reference if one was used
            if (SelectedTemplate != null)
            {
                metadata["template-id"] = SelectedTemplate.Id.ToString();
                metadata["template-name"] = SelectedTemplate.TemplateName;
            }
            
            // Try to extract MSI product code if it's an MSI file
            if (Path.GetExtension(LocalFilePath).Equals(".msi", StringComparison.OrdinalIgnoreCase))
            {
                try
                {
                    var installerService = _serviceProvider.GetService<IEnhancedInstallerService>();
                    if (installerService != null)
                    {
                        var productCode = await installerService.ExtractMsiProductCodeAsync(LocalFilePath);
                        if (!string.IsNullOrEmpty(productCode))
                        {
                            metadata["msi-product-code"] = productCode;
                            _logger.LogInformation("Extracted MSI Product Code: {ProductCode}", productCode);
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to extract MSI product code");
                }
            }

            // Create progress handler
            var progressHandler = new Progress<UploadProgress>(progress =>
            {
                UploadProgress = progress.PercentComplete;
                
                // Format bytes uploaded
                BytesUploaded = FormatBytes(progress.BytesUploaded) + " / " + FormatBytes(progress.TotalBytes);
                
                // Format upload speed
                UploadSpeed = FormatBytes(progress.BytesPerSecond) + "/s";
                
                // Format time remaining
                if (progress.TimeRemaining.TotalHours >= 1)
                    TimeRemaining = $"{(int)progress.TimeRemaining.TotalHours}h {progress.TimeRemaining.Minutes}m {progress.TimeRemaining.Seconds}s";
                else if (progress.TimeRemaining.TotalMinutes >= 1)
                    TimeRemaining = $"{(int)progress.TimeRemaining.TotalMinutes}m {progress.TimeRemaining.Seconds}s";
                else
                    TimeRemaining = $"{(int)progress.TimeRemaining.TotalSeconds}s";
                
                // Format time elapsed
                if (progress.TimeElapsed.TotalHours >= 1)
                    TimeElapsed = $"{(int)progress.TimeElapsed.TotalHours}h {progress.TimeElapsed.Minutes}m {progress.TimeElapsed.Seconds}s";
                else if (progress.TimeElapsed.TotalMinutes >= 1)
                    TimeElapsed = $"{(int)progress.TimeElapsed.TotalMinutes}m {progress.TimeElapsed.Seconds}s";
                else
                    TimeElapsed = $"{(int)progress.TimeElapsed.TotalSeconds}s";
                
                // Update status
                if (progress.Status == "Complete")
                {
                    StatusMessage = $"Upload complete! Average speed: {UploadSpeed}";
                }
                else
                {
                    StatusMessage = $"Uploading {FileName}... {progress.PercentComplete:F1}%";
                }
            });
            
            // Get file size
            var fileInfo = new FileInfo(LocalFilePath);
            TotalBytes = fileInfo.Length;
            
            // Upload file to GCS with metadata and progress tracking
            var mediaLink = await _cloudStorageService.UploadFileWithProgressAsync(
                LocalFilePath,
                CloudPath,
                metadata,
                progressHandler);

            // Add to recent uploads
            RecentUploads.Insert(0, $"{SoftwareName} v{Version} → {CloudPath}");
            if (RecentUploads.Count > 10)
            {
                RecentUploads.RemoveAt(RecentUploads.Count - 1);
            }

            // Success! Metadata is automatically stored with the object
            StatusMessage = $"Successfully uploaded: {SoftwareName} v{Version}";
            UploadProgress = 100;
            
            _logger.LogInformation("Uploaded software with metadata: {Name} v{Version} to {Path}", 
                SoftwareName, Version, CloudPath);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to upload file");
            StatusMessage = $"Upload failed: {ex.Message}";
        }
        finally
        {
            IsUploading = false;
            
            // Reset progress indicators after a delay
            await Task.Delay(3000);
            UploadSpeed = "";
            TimeRemaining = "";
            TimeElapsed = "";
            BytesUploaded = "";
        }
    }


    [RelayCommand]
    private async Task ListBucketContentsAsync()
    {
        if (_cloudStorageService == null)
        {
            StatusMessage = "Cloud storage service not configured";
            return;
        }

        try
        {
            StatusMessage = "Listing bucket contents...";
            var files = await _cloudStorageService.ListFilesAsync("software/");
            
            RecentUploads.Clear();
            foreach (var file in files.Take(20))
            {
                RecentUploads.Add(file);
            }
            
            StatusMessage = $"Found {files.Count()} files in bucket";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to list bucket contents");
            StatusMessage = $"Failed to list files: {ex.Message}";
        }
    }

    [RelayCommand]
    private void CopyCloudPath()
    {
        if (!string.IsNullOrEmpty(CloudPath))
        {
            System.Windows.Clipboard.SetText(CloudPath);
            StatusMessage = "Cloud path copied to clipboard";
        }
    }

    [RelayCommand]
    private async Task GenerateSignedUrlAsync()
    {
        if (_cloudStorageService == null || string.IsNullOrEmpty(CloudPath))
        {
            StatusMessage = "Cannot generate signed URL";
            return;
        }

        try
        {
            var url = await _cloudStorageService.GenerateSignedUrlAsync(
                CloudPath, 
                TimeSpan.FromHours(SignedUrlHours));
                
            System.Windows.Clipboard.SetText(url);
            StatusMessage = $"Signed URL copied to clipboard (expires in {SignedUrlHours} hours)";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to generate signed URL");
            StatusMessage = $"Failed to generate URL: {ex.Message}";
        }
    }
    
    private static string FormatBytes(double bytes)
    {
        string[] sizes = { "B", "KB", "MB", "GB", "TB" };
        int order = 0;
        while (bytes >= 1024 && order < sizes.Length - 1)
        {
            order++;
            bytes /= 1024;
        }
        return $"{bytes:F2} {sizes[order]}";
    }
    
    partial void OnSelectedTemplateChanged(SoftwareTemplate? value)
    {
        if (value != null)
        {
            ApplyTemplate();
        }
    }
    
    private void ApplyTemplate()
    {
        if (SelectedTemplate == null) return;
        
        // Apply template values to form fields
        if (!string.IsNullOrWhiteSpace(SelectedTemplate.SoftwareName))
            SoftwareName = SelectedTemplate.SoftwareName;
        if (!string.IsNullOrWhiteSpace(SelectedTemplate.Version))
            Version = SelectedTemplate.Version;
        if (SelectedTemplate.Type.HasValue)
            SelectedType = SelectedTemplate.Type.Value;
        if (!string.IsNullOrWhiteSpace(SelectedTemplate.InstallPath))
            ExpectedInstallPath = SelectedTemplate.InstallPath;
        if (!string.IsNullOrWhiteSpace(SelectedTemplate.MainExecutable))
            MainExecutable = SelectedTemplate.MainExecutable;
        if (!string.IsNullOrWhiteSpace(SelectedTemplate.InstallArguments))
            InstallArguments = SelectedTemplate.InstallArguments;
        if (!string.IsNullOrWhiteSpace(SelectedTemplate.UninstallCommand))
            UninstallCommand = SelectedTemplate.UninstallCommand;
        if (SelectedTemplate.RequiresElevation.HasValue)
            RequiresElevation = SelectedTemplate.RequiresElevation.Value;
        if (SelectedTemplate.IsZipPackage.HasValue)
            IsZipPackage = SelectedTemplate.IsZipPackage.Value;
        if (SelectedTemplate.CleanupAfterInstall.HasValue)
            CleanupAfterInstall = SelectedTemplate.CleanupAfterInstall.Value;
        if (!string.IsNullOrWhiteSpace(SelectedTemplate.DefaultCloudPath) && !string.IsNullOrEmpty(FileName))
            CloudPath = SelectedTemplate.DefaultCloudPath + FileName;
            
        StatusMessage = $"Applied template: {SelectedTemplate.TemplateName}";
    }
}