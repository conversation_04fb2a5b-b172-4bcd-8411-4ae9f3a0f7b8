# Google Cloud Storage Setup Guide

This guide will help you set up Google Cloud Storage (GCS) for the S-Tier Downloader application.

## Prerequisites

1. A Google Cloud Platform (GCP) account
2. A GCP project with billing enabled
3. Google Cloud SDK installed (optional, for CLI management)

## Step 1: Create a Storage Bucket

1. Go to the [Google Cloud Console](https://console.cloud.google.com/)
2. Navigate to **Storage** > **Browser**
3. Click **Create Bucket**
4. Configure your bucket:
   - **Name**: Choose a globally unique name (e.g., `your-company-bas-software`)
   - **Location**: Choose a region close to your users
   - **Storage class**: Standard (for frequently accessed files)
   - **Access control**: Uniform (recommended)
5. Click **Create**

## Step 2: Create a Service Account

1. Navigate to **IAM & Admin** > **Service Accounts**
2. Click **Create Service Account**
3. Fill in the details:
   - **Name**: `s-tier-downloader`
   - **Description**: Service account for S-Tier Downloader application
4. Click **Create and Continue**
5. <PERSON> the following roles:
   - `Storage Object Viewer` (for read-only access)
   - `Storage Object Creator` (if you need upload capability)
6. Click **Continue** then **Done**

## Step 3: Generate Service Account Key

1. Click on the created service account
2. Go to the **Keys** tab
3. Click **Add Key** > **Create new key**
4. Choose **JSON** format
5. Click **Create**
6. Save the downloaded JSON file securely

## Step 4: Configure the Application

### Option A: Using Service Account Key File

1. Place the JSON key file in a secure location on your server
2. Update `appsettings.json`:

```json
{
  "CloudStorage": {
    "Provider": "GoogleCloud",
    "ProjectId": "your-project-id",
    "BucketName": "your-bucket-name",
    "CredentialsPath": "C:\\Path\\To\\service-account-key.json",
    "UseApplicationDefaultCredentials": false,
    "EnableCaching": true
  }
}
```

### Option B: Using Application Default Credentials (Recommended for GCE/GKE)

If running on Google Compute Engine, Google Kubernetes Engine, or Cloud Run:

```json
{
  "CloudStorage": {
    "Provider": "GoogleCloud",
    "ProjectId": "your-project-id",
    "BucketName": "your-bucket-name",
    "UseApplicationDefaultCredentials": true,
    "EnableCaching": true
  }
}
```

### Option C: Using Environment Variable

Set the environment variable:
```powershell
$env:GOOGLE_APPLICATION_CREDENTIALS="C:\Path\To\service-account-key.json"
```

Then use Application Default Credentials in config:
```json
{
  "CloudStorage": {
    "UseApplicationDefaultCredentials": true
  }
}
```

## Step 5: Upload Software to GCS

### Using Google Cloud Console

1. Navigate to your bucket in the Storage Browser
2. Create folders for organization (e.g., `software/niagara/`, `software/vpn/`)
3. Click **Upload Files** to upload installers
4. Note the object path for configuration

### Using gsutil (Command Line)

```bash
# Upload a single file
gsutil cp installer.exe gs://your-bucket-name/software/

# Upload with custom metadata
gsutil -h "Content-Type:application/octet-stream" \
       -h "Cache-Control:public, max-age=3600" \
       cp installer.exe gs://your-bucket-name/software/

# Upload entire directory
gsutil -m cp -r ./installers/* gs://your-bucket-name/software/
```

## Step 6: Configure Software Entries

Update your `appsettings.json` with GCS-based software:

```json
{
  "Software": [
    {
      "Name": "Tridium Niagara 4",
      "Version": "4.13",
      "Type": "TridiumNiagara",
      "StorageLocation": "GoogleCloudStorage",
      "CloudObjectName": "software/niagara/niagara-4.13-installer.exe",
      "UseSignedUrl": false,
      "InstallArguments": "/S /v/qn",
      "ChecksumSha256": "abc123...",
      "RequiresElevation": true
    },
    {
      "Name": "FortiClient VPN",
      "Version": "7.2.0",
      "Type": "VpnClient",
      "StorageLocation": "GoogleCloudStorage",
      "CloudObjectName": "software/vpn/forticlient-7.2.0.msi",
      "UseSignedUrl": true,
      "SignedUrlExpirationHours": 24,
      "InstallArguments": "/quiet",
      "RequiresElevation": true
    }
  ]
}
```

## Security Best Practices

1. **Principle of Least Privilege**: Only grant necessary permissions to service accounts
2. **Key Rotation**: Regularly rotate service account keys
3. **Secure Storage**: Never commit service account keys to version control
4. **Use Signed URLs**: For temporary access without credentials
5. **Enable Audit Logging**: Track access to your storage bucket
6. **Encryption**: GCS encrypts data at rest by default
7. **Versioning**: Enable object versioning for critical installers

## Bucket Lifecycle Policies

To manage storage costs, configure lifecycle rules:

1. Go to your bucket's **Lifecycle** tab
2. Add rules for:
   - Delete old versions after X days
   - Move infrequently accessed files to Nearline/Coldline storage
   - Delete temporary/cache files

Example lifecycle configuration:
```json
{
  "lifecycle": {
    "rule": [
      {
        "action": {"type": "Delete"},
        "condition": {
          "age": 365,
          "matchesPrefix": ["temp/", "cache/"]
        }
      },
      {
        "action": {"type": "SetStorageClass", "storageClass": "NEARLINE"},
        "condition": {
          "age": 90,
          "matchesPrefix": ["archive/"]
        }
      }
    ]
  }
}
```

## Monitoring and Alerts

1. Set up monitoring in **Operations** > **Monitoring**
2. Create alerts for:
   - High download rates (potential abuse)
   - Failed authentication attempts
   - Unusual access patterns
3. Review Cloud Storage metrics:
   - Total bytes stored
   - Network egress
   - Request counts

## Cost Optimization

1. **Regional Storage**: Use single-region buckets when possible
2. **Compression**: Compress installers before uploading
3. **CDN Integration**: Use Cloud CDN for frequently accessed files
4. **Egress Costs**: Be aware of data transfer costs, especially for large files

## Troubleshooting

### Common Issues

1. **403 Forbidden Error**
   - Check service account permissions
   - Verify bucket name and object paths
   - Ensure credentials are properly configured

2. **404 Not Found**
   - Verify object exists in bucket
   - Check for typos in object name
   - Ensure correct bucket is specified

3. **Authentication Failed**
   - Verify service account key is valid
   - Check credentials path in configuration
   - Ensure environment variables are set correctly

### Debug Logging

Enable detailed logging in `appsettings.json`:
```json
{
  "Logging": {
    "LogLevel": {
      "STierDownloader.Core.Services.CloudStorageService": "Debug",
      "Google": "Information"
    }
  }
}
```

## Support

For GCS-specific issues, consult:
- [Google Cloud Storage Documentation](https://cloud.google.com/storage/docs)
- [GCS Client Library for .NET](https://cloud.google.com/dotnet/docs/reference/Google.Cloud.Storage.V1/latest)