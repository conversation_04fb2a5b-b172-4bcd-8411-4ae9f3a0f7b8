using System.Text.Json;
using Microsoft.Extensions.Logging;
using STierDownloader.Core.Models;

namespace STierDownloader.Core.Services;

public interface IBrandManagementService
{
    Task<List<NiagaraBrand>> GetBrandsAsync();
    Task<NiagaraBrand?> GetBrandByIdAsync(Guid id);
    Task<NiagaraBrand?> GetBrandByNameAsync(string name);
    Task<NiagaraBrand> AddBrandAsync(NiagaraBrand brand);
    Task<NiagaraBrand?> UpdateBrandAsync(NiagaraBrand brand);
    Task<bool> DeleteBrandAsync(Guid id);
    Task<bool> SaveBrandsAsync();
    Task<bool> LoadBrandsAsync();
    Task ResetToDefaultBrandsAsync();
}

public class BrandManagementService : IBrandManagementService
{
    private readonly ILogger<BrandManagementService> _logger;
    private readonly string _configFilePath;
    private NiagaraBrandsConfiguration _configuration;
    private readonly JsonSerializerOptions _jsonOptions;
    
    public BrandManagementService(ILogger<BrandManagementService> logger)
    {
        _logger = logger;
        
        // Store brands configuration in AppData
        var appDataPath = Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData);
        var configDir = Path.Combine(appDataPath, "STierDownloader", "Config");
        Directory.CreateDirectory(configDir);
        
        _configFilePath = Path.Combine(configDir, "niagara-brands.json");
        
        _jsonOptions = new JsonSerializerOptions
        {
            WriteIndented = true,
            PropertyNameCaseInsensitive = true
        };
        
        _configuration = new NiagaraBrandsConfiguration();
        
        // Load brands on initialization
        Task.Run(async () => await LoadBrandsAsync());
    }
    
    public async Task<List<NiagaraBrand>> GetBrandsAsync()
    {
        if (_configuration.Brands.Count == 0)
        {
            await LoadBrandsAsync();
        }
        
        return _configuration.Brands.OrderBy(b => b.DisplayName).ToList();
    }
    
    public async Task<NiagaraBrand?> GetBrandByIdAsync(Guid id)
    {
        var brands = await GetBrandsAsync();
        return brands.FirstOrDefault(b => b.Id == id);
    }
    
    public async Task<NiagaraBrand?> GetBrandByNameAsync(string name)
    {
        var brands = await GetBrandsAsync();
        return brands.FirstOrDefault(b => 
            string.Equals(b.Name, name, StringComparison.OrdinalIgnoreCase) ||
            string.Equals(b.DisplayName, name, StringComparison.OrdinalIgnoreCase));
    }
    
    public async Task<NiagaraBrand> AddBrandAsync(NiagaraBrand brand)
    {
        if (string.IsNullOrWhiteSpace(brand.Name))
        {
            throw new ArgumentException("Brand name is required");
        }
        
        if (string.IsNullOrWhiteSpace(brand.DisplayName))
        {
            brand.DisplayName = brand.Name;
        }
        
        if (string.IsNullOrWhiteSpace(brand.InstallPath))
        {
            throw new ArgumentException("Install path is required");
        }
        
        // Check for duplicate names
        var existing = await GetBrandByNameAsync(brand.Name);
        if (existing != null)
        {
            throw new InvalidOperationException($"Brand with name '{brand.Name}' already exists");
        }
        
        brand.Id = Guid.NewGuid();
        brand.CreatedDate = DateTime.Now;
        brand.ModifiedDate = DateTime.Now;
        brand.IsBuiltIn = false; // User-created brands are never built-in
        
        _configuration.Brands.Add(brand);
        _configuration.LastModified = DateTime.Now;
        
        await SaveBrandsAsync();
        
        _logger.LogInformation("Added new brand: {Name} ({DisplayName})", brand.Name, brand.DisplayName);
        
        return brand;
    }
    
    public async Task<NiagaraBrand?> UpdateBrandAsync(NiagaraBrand brand)
    {
        var existing = _configuration.Brands.FirstOrDefault(b => b.Id == brand.Id);
        if (existing == null)
        {
            _logger.LogWarning("Brand not found for update: {Id}", brand.Id);
            return null;
        }
        
        // Allow editing all brands now
        // Check for duplicate names (excluding self)
        var duplicate = _configuration.Brands.FirstOrDefault(b => 
            b.Id != brand.Id && 
            (string.Equals(b.Name, brand.Name, StringComparison.OrdinalIgnoreCase) ||
             string.Equals(b.DisplayName, brand.DisplayName, StringComparison.OrdinalIgnoreCase)));
        
        if (duplicate != null)
        {
            throw new InvalidOperationException($"Brand with name '{brand.Name}' already exists");
        }
        
        existing.Name = brand.Name;
        existing.DisplayName = brand.DisplayName;
        existing.InstallPath = brand.InstallPath;
        existing.MainExecutable = brand.MainExecutable;
        existing.InstallArguments = brand.InstallArguments;
        existing.Description = brand.Description;
        existing.ModifiedDate = DateTime.Now;
        
        _configuration.LastModified = DateTime.Now;
        await SaveBrandsAsync();
        
        _logger.LogInformation("Updated brand: {Name} ({DisplayName})", existing.Name, existing.DisplayName);
        
        return existing;
    }
    
    public async Task<bool> DeleteBrandAsync(Guid id)
    {
        var brand = _configuration.Brands.FirstOrDefault(b => b.Id == id);
        if (brand == null)
        {
            _logger.LogWarning("Brand not found for deletion: {Id}", id);
            return false;
        }
        
        // Don't allow deletion if it's the last brand
        if (_configuration.Brands.Count <= 1)
        {
            _logger.LogWarning("Cannot delete the last remaining brand: {Name}", brand.Name);
            throw new InvalidOperationException($"Cannot delete the last remaining brand '{brand.DisplayName}'. At least one brand must exist.");
        }
        
        _configuration.Brands.Remove(brand);
        _configuration.LastModified = DateTime.Now;
        
        await SaveBrandsAsync();
        
        _logger.LogInformation("Deleted brand: {Name} ({DisplayName})", brand.Name, brand.DisplayName);
        
        return true;
    }
    
    public async Task<bool> SaveBrandsAsync()
    {
        try
        {
            var json = JsonSerializer.Serialize(_configuration, _jsonOptions);
            await File.WriteAllTextAsync(_configFilePath, json);
            
            _logger.LogDebug("Saved {Count} brands to {Path}", _configuration.Brands.Count, _configFilePath);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to save brands configuration");
            return false;
        }
    }
    
    public async Task<bool> LoadBrandsAsync()
    {
        try
        {
            if (!File.Exists(_configFilePath))
            {
                _logger.LogInformation("Brands configuration not found, creating default");
                _configuration = NiagaraBrandsConfiguration.CreateDefault();
                await SaveBrandsAsync();
                return true;
            }
            
            var json = await File.ReadAllTextAsync(_configFilePath);
            var config = JsonSerializer.Deserialize<NiagaraBrandsConfiguration>(json, _jsonOptions);
            
            if (config != null && config.Brands.Count > 0)
            {
                _configuration = config;
                _logger.LogInformation("Loaded {Count} brands from configuration", _configuration.Brands.Count);
                return true;
            }
            else
            {
                _logger.LogWarning("Invalid or empty brands configuration, using defaults");
                _configuration = NiagaraBrandsConfiguration.CreateDefault();
                await SaveBrandsAsync();
                return true;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to load brands configuration, using defaults");
            _configuration = NiagaraBrandsConfiguration.CreateDefault();
            return false;
        }
    }
    
    public async Task ResetToDefaultBrandsAsync()
    {
        _logger.LogInformation("Resetting brands to defaults");
        _configuration = NiagaraBrandsConfiguration.CreateDefault();
        await SaveBrandsAsync();
    }
}