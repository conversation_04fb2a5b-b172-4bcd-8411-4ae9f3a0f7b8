using System.Text.Json.Serialization;

namespace STierDownloader.Core.Models;

public class NiagaraBrand
{
    public Guid Id { get; set; } = Guid.NewGuid();
    public string Name { get; set; } = string.Empty;
    public string DisplayName { get; set; } = string.Empty;
    public string InstallPath { get; set; } = string.Empty;
    public string MainExecutable { get; set; } = "wb.exe";
    public string? InstallArguments { get; set; }
    public string? Description { get; set; }
    public bool IsBuiltIn { get; set; } = false;
    public DateTime CreatedDate { get; set; } = DateTime.Now;
    public DateTime ModifiedDate { get; set; } = DateTime.Now;
    
    // For backward compatibility with the enum
    [JsonIgnore]
    public NiagaraVariant? LegacyVariant { get; set; }
}

public class NiagaraBrandsConfiguration
{
    public List<NiagaraBrand> Brands { get; set; } = new();
    public DateTime LastModified { get; set; } = DateTime.Now;
    public string Version { get; set; } = "1.0";
    
    // Create default brands
    public static NiagaraBrandsConfiguration CreateDefault()
    {
        return new NiagaraBrandsConfiguration
        {
            Brands = new List<NiagaraBrand>
            {
                new NiagaraBrand
                {
                    Name = "Standard",
                    DisplayName = "Tridium Niagara 4",
                    InstallPath = @"C:\Niagara\Niagara-4.4",
                    Description = "Standard Tridium Niagara 4 installation",
                    IsBuiltIn = true,
                    LegacyVariant = NiagaraVariant.Standard
                },
                new NiagaraBrand
                {
                    Name = "Vykon",
                    DisplayName = "Vykon by Tridium",
                    InstallPath = @"C:\Vykon",
                    Description = "Tridium's Vykon brand for building automation",
                    IsBuiltIn = true,
                    LegacyVariant = NiagaraVariant.Vykon
                },
                new NiagaraBrand
                {
                    Name = "Honeywell",
                    DisplayName = "Honeywell WEBs-N4",
                    InstallPath = @"C:\Honeywell\WEBs-N4",
                    Description = "Honeywell's WEBs-N4 platform based on Niagara",
                    IsBuiltIn = true,
                    LegacyVariant = NiagaraVariant.Honeywell
                },
                new NiagaraBrand
                {
                    Name = "Distech",
                    DisplayName = "Distech EC-Net",
                    InstallPath = @"C:\Distech\EC-Net",
                    Description = "Distech Controls EC-Net platform",
                    IsBuiltIn = true,
                    LegacyVariant = NiagaraVariant.Distech
                },
                new NiagaraBrand
                {
                    Name = "Lynxspring",
                    DisplayName = "Lynxspring JENEsys",
                    InstallPath = @"C:\Lynxspring\JENEsys",
                    Description = "Lynxspring JENEsys Edge Controller platform",
                    IsBuiltIn = true,
                    LegacyVariant = NiagaraVariant.Lynxspring
                },
                new NiagaraBrand
                {
                    Name = "Johnson",
                    DisplayName = "Johnson Controls FX",
                    InstallPath = @"C:\JohnsonControls\FX",
                    Description = "Johnson Controls FX Supervisory Controller",
                    IsBuiltIn = false
                },
                new NiagaraBrand
                {
                    Name = "Siemens",
                    DisplayName = "Siemens Desigo CC",
                    InstallPath = @"C:\Siemens\DesigoCC",
                    Description = "Siemens Desigo Control Center",
                    IsBuiltIn = false
                },
                new NiagaraBrand
                {
                    Name = "Schneider",
                    DisplayName = "Schneider EcoStruxure",
                    InstallPath = @"C:\Schneider\EcoStruxure",
                    Description = "Schneider Electric EcoStruxure Building Operation",
                    IsBuiltIn = false
                }
            }
        };
    }
}