# Google Cloud Storage Setup Checklist

## ✅ Configuration Complete

Your application is now configured to use Google Cloud Storage with:
- **Project ID**: `s-tier-downloader`
- **Service Account**: `<EMAIL>`
- **Credentials**: Stored in `gcs-credentials.json`

## 🚀 Next Steps

### 1. Create Your Storage Bucket

Run this command in Google Cloud Shell or with `gcloud` CLI:
```bash
gsutil mb -p s-tier-downloader -c STANDARD -l us-central1 gs://s-tier-downloader-software/
```

Or create via Console:
1. Go to [Google Cloud Console](https://console.cloud.google.com/storage/browser?project=s-tier-downloader)
2. Click "Create Bucket"
3. Name: `s-tier-downloader-software`
4. Location: Choose nearest to your users
5. Storage class: Standard

### 2. Set Bucket Permissions

Grant your service account access:
```bash
gsutil iam ch serviceAccount:<EMAIL>:objectViewer gs://s-tier-downloader-software/
gsutil iam ch serviceAccount:<EMAIL>:objectCreator gs://s-tier-downloader-software/
```

### 3. Upload Your Software

Create organized folders:
```bash
# Upload Niagara installer
gsutil cp niagara-4.13-installer.exe gs://s-tier-downloader-software/software/niagara/

# Upload VPN clients
gsutil cp forticlient-7.2.msi gs://s-tier-downloader-software/software/vpn/

# Upload HVAC tools
gsutil cp hvac-config-tool.exe gs://s-tier-downloader-software/software/tools/
```

### 4. Update Software Entries

Edit `appsettings.json` to point to your GCS files:
```json
{
  "Name": "Tridium Niagara 4",
  "Version": "4.13",
  "Type": "TridiumNiagara",
  "StorageLocation": "GoogleCloudStorage",
  "CloudObjectName": "software/niagara/niagara-4.13-installer.exe",
  "InstallArguments": "/S /v/qn",
  "RequiresElevation": true
}
```

### 5. Test the Integration

1. Run the application as Administrator
2. Try downloading a test file
3. Check the cache directory: `%LOCALAPPDATA%\STierDownloader\Cache`
4. Verify installation works

## 🔒 Security Reminders

⚠️ **CRITICAL**: Your `gcs-credentials.json` file contains sensitive credentials!

- ✅ Added to `.gitignore` - won't be committed
- ❌ Never share this file publicly
- ❌ Never post it in issues or forums
- ✅ Consider using environment variables in production

## 📊 Monitoring

Set up monitoring in GCP Console:
1. Storage metrics: Track downloads and bandwidth
2. Set budget alerts to avoid surprises
3. Enable access logs for audit trail

## 🧪 Testing Commands

Test bucket access:
```bash
# List bucket contents
gsutil ls gs://s-tier-downloader-software/

# Check permissions
gsutil iam get gs://s-tier-downloader-software/

# Upload test file
echo "test" > test.txt
gsutil cp test.txt gs://s-tier-downloader-software/test/
gsutil rm gs://s-tier-downloader-software/test/test.txt
```

## 📝 Notes

- Bucket name `s-tier-downloader-software` must be globally unique
- If taken, use something like `s-tier-downloader-software-[company]`
- Update `appsettings.json` with your actual bucket name
- Consider separate buckets for dev/staging/production

## Support

- [Google Cloud Storage Documentation](https://cloud.google.com/storage/docs)
- [gsutil Tool Reference](https://cloud.google.com/storage/docs/gsutil)
- [IAM Permissions Reference](https://cloud.google.com/storage/docs/access-control/iam-permissions)