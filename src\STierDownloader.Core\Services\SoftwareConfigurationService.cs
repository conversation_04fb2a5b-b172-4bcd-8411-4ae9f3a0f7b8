using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using STierDownloader.Core.Models;
using System.Text.Json;

namespace STierDownloader.Core.Services;

public interface ISoftwareConfigurationService
{
    List<Software> GetConfiguredSoftware();
    Task<List<Software>> GetConfiguredSoftwareAsync();
    void ReloadConfiguration();
}

public class SoftwareConfigurationService : ISoftwareConfigurationService
{
    private readonly IConfiguration _configuration;
    private readonly ILogger<SoftwareConfigurationService> _logger;
    private List<Software> _cachedSoftware = new();
    private readonly object _cacheLock = new();

    public SoftwareConfigurationService(IConfiguration configuration, ILogger<SoftwareConfigurationService> logger)
    {
        _configuration = configuration;
        _logger = logger;
        LoadSoftwareFromConfiguration();
    }

    public List<Software> GetConfiguredSoftware()
    {
        lock (_cacheLock)
        {
            return new List<Software>(_cachedSoftware);
        }
    }

    public Task<List<Software>> GetConfiguredSoftwareAsync()
    {
        return Task.FromResult(GetConfiguredSoftware());
    }

    public void ReloadConfiguration()
    {
        LoadSoftwareFromConfiguration();
    }

    private void LoadSoftwareFromConfiguration()
    {
        try
        {
            var softwareList = new List<Software>();
            var softwareSection = _configuration.GetSection("Software");
            
            if (!softwareSection.Exists())
            {
                _logger.LogWarning("No 'Software' section found in configuration");
                lock (_cacheLock)
                {
                    _cachedSoftware = softwareList;
                }
                return;
            }

            var softwareItems = softwareSection.Get<List<SoftwareConfig>>();
            
            if (softwareItems == null || softwareItems.Count == 0)
            {
                _logger.LogWarning("Software section exists but contains no items");
                lock (_cacheLock)
                {
                    _cachedSoftware = softwareList;
                }
                return;
            }

            foreach (var item in softwareItems)
            {
                try
                {
                    // Skip items without required fields
                    if (string.IsNullOrWhiteSpace(item.Name))
                    {
                        _logger.LogWarning("Skipping software item with no name");
                        continue;
                    }

                    // Skip items without any download source
                    if (string.IsNullOrWhiteSpace(item.DownloadUrl) && 
                        string.IsNullOrWhiteSpace(item.CloudObjectName))
                    {
                        _logger.LogWarning("Skipping {Name} - no download URL or cloud object specified", item.Name);
                        continue;
                    }

                    var software = new Software
                    {
                        Name = item.Name,
                        Version = item.Version ?? "Unknown",
                        DownloadUrl = item.DownloadUrl ?? string.Empty,
                        InstallArguments = item.InstallArguments,
                        UninstallCommand = item.UninstallCommand,
                        RequiresElevation = item.RequiresElevation ?? true,
                        FileSize = item.FileSize ?? 0,
                        ChecksumSha256 = item.ChecksumSha256,
                        CloudObjectName = item.CloudObjectName,
                        CloudBucketOverride = item.CloudBucketOverride,
                        UseSignedUrl = item.UseSignedUrl ?? false,
                        SignedUrlExpirationHours = item.SignedUrlExpirationHours ?? 24
                    };

                    // Parse SoftwareType
                    if (!string.IsNullOrWhiteSpace(item.Type))
                    {
                        if (Enum.TryParse<SoftwareType>(item.Type, true, out var type))
                        {
                            software.Type = type;
                        }
                        else
                        {
                            _logger.LogWarning("Unknown software type '{Type}' for {Name}, defaulting to Utility", 
                                item.Type, item.Name);
                            software.Type = SoftwareType.Utility;
                        }
                    }

                    // Parse StorageLocation
                    if (!string.IsNullOrWhiteSpace(item.StorageLocation))
                    {
                        if (Enum.TryParse<StorageLocation>(item.StorageLocation, true, out var location))
                        {
                            software.StorageLocation = location;
                        }
                        else
                        {
                            _logger.LogWarning("Unknown storage location '{Location}' for {Name}, defaulting to Http", 
                                item.StorageLocation, item.Name);
                            software.StorageLocation = StorageLocation.Http;
                        }
                    }

                    softwareList.Add(software);
                    _logger.LogInformation("Loaded software: {Name} v{Version} ({Type})", 
                        software.Name, software.Version, software.Type);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error loading software item from configuration");
                }
            }

            lock (_cacheLock)
            {
                _cachedSoftware = softwareList;
            }

            _logger.LogInformation("Successfully loaded {Count} software items from configuration", softwareList.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to load software configuration");
            lock (_cacheLock)
            {
                _cachedSoftware = new List<Software>();
            }
        }
    }
}

// Configuration model that matches the JSON structure
public class SoftwareConfig
{
    public string? Name { get; set; }
    public string? Version { get; set; }
    public string? Type { get; set; }
    public string? DownloadUrl { get; set; }
    public string? InstallArguments { get; set; }
    public string? UninstallCommand { get; set; }
    public bool? RequiresElevation { get; set; }
    public long? FileSize { get; set; }
    public string? ChecksumSha256 { get; set; }
    public string? StorageLocation { get; set; }
    public string? CloudObjectName { get; set; }
    public string? CloudBucketOverride { get; set; }
    public bool? UseSignedUrl { get; set; }
    public int? SignedUrlExpirationHours { get; set; }
}